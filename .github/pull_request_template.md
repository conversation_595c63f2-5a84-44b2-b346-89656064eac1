## Description

Brief description of the changes in this PR.

## Type of Change

- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update
- [ ] Performance improvement
- [ ] Code refactoring
- [ ] Security improvement

## Related Issues

Fixes #(issue number)
Closes #(issue number)
Related to #(issue number)

## Changes Made

- [ ] Change 1
- [ ] Change 2
- [ ] Change 3

## Testing

- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed
- [ ] Security testing completed (if applicable)
- [ ] Performance testing completed (if applicable)

### Test Instructions

1. Step 1
2. Step 2
3. Step 3

## Screenshots (if applicable)

Before:
[Add screenshot]

After:
[Add screenshot]

## Checklist

- [ ] My code follows the style guidelines of this project
- [ ] I have performed a self-review of my own code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] My changes generate no new warnings
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes
- [ ] Any dependent changes have been merged and published

## Security Considerations

- [ ] No sensitive data exposed
- [ ] Input validation implemented
- [ ] Authentication/authorization properly handled
- [ ] No new security vulnerabilities introduced

## Performance Impact

- [ ] No performance degradation
- [ ] Performance improvements included
- [ ] Database queries optimized
- [ ] Caching implemented where appropriate

## Documentation

- [ ] README updated
- [ ] API documentation updated
- [ ] User guide updated
- [ ] Deployment guide updated
- [ ] Code comments added/updated
