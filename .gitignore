# FORMS System .gitignore

# Environment files (NEVER commit these)
.env
.env.local
.env.production
.env.staging
deployment/.env
deployment/.env.*
!deployment/.env.example

# Secrets and certificates
*.pem
*.key
*.crt
*.p12
*.pfx
deployment/docker/ssl/
secrets/
certificates/

# Database files
*.db
*.sqlite
*.sqlite3
data/
postgres-data/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
deployment/logs/
backend/logs/
frontend/logs/

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/
jspm_packages/
target/
Cargo.lock

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.test

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out/
frontend/.next/
frontend/out/

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/
.tmp/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage
*.lcov

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules/
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Rust
target/
Cargo.lock
**/*.rs.bk

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
.dockerignore

# Backup files
*.backup
*.bak
*.old
deployment/backups/
backups/

# Test files
test-results/
coverage/
.nyc_output/

# Build artifacts
build/
dist/
out/

# Package files
*.tar.gz
*.zip
*.rar

# Security audit reports
deployment/security-audit/reports/
security-reports/

# Monitoring data
prometheus-data/
grafana-data/

# Local development
.local/
local/

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# Ansible
*.retry

# Kubernetes
kubeconfig

# Cloud provider configs
.aws/
.gcp/
.azure/

# Local configuration overrides
docker-compose.override.yml
docker-compose.local.yml

# Generated documentation
docs/generated/

# Cache directories
.cache/
cache/

# Temporary files
*.tmp
*.temp

# Editor backups
*~
*.orig

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# Java
*.class
*.jar
*.war
*.ear
*.zip
*.tar.gz
*.rar

# Gradle
.gradle/
build/

# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties

# IntelliJ
*.iml
*.ipr
*.iws
.idea/

# Eclipse
.classpath
.project
.settings/
bin/

# NetBeans
nbproject/private/
nbbuild/
dist/
nbdist/
.nb-gradle/

# Visual Studio
*.suo
*.user
*.userosscache
*.sln.docstates
*.userprefs
bin/
obj/

# Xamarin
*.userprefs

# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
build/
bld/
[Bb]in/
[Oo]bj/

# MSTest test Results
[Tt]est[Rr]esult*/
[Bb]uild[Ll]og.*
