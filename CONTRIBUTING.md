# Contributing to FORMS System

Thank you for your interest in contributing to the FORMS System! This document provides guidelines and information for contributors.

## Code of Conduct

By participating in this project, you agree to abide by our Code of Conduct. Please be respectful and constructive in all interactions.

## How to Contribute

### Reporting Issues

1. **Search existing issues** first to avoid duplicates
2. **Use the issue template** when creating new issues
3. **Provide detailed information** including:
   - Steps to reproduce
   - Expected vs actual behavior
   - Environment details
   - Screenshots if applicable

### Suggesting Features

1. **Check the roadmap** to see if the feature is already planned
2. **Open a feature request** with detailed description
3. **Explain the use case** and benefits
4. **Consider implementation complexity**

### Pull Requests

1. **Fork the repository** and create a feature branch
2. **Follow coding standards** (see below)
3. **Write tests** for new functionality
4. **Update documentation** as needed
5. **Submit a pull request** with clear description

## Development Setup

### Prerequisites

- Docker and Docker Compose
- Node.js 20+
- Rust 1.87+
- Git

### Local Development

1. **Clone your fork**
   ```bash
   git clone https://github.com/your-username/forms-system.git
   cd forms-system
   ```

2. **Set up environment**
   ```bash
   cp deployment/.env.example deployment/.env
   # Edit .env with your configuration
   ```

3. **Start development environment**
   ```bash
   docker-compose up -d
   ```

4. **Run tests**
   ```bash
   # Backend tests
   cd backend && cargo test
   
   # Frontend tests
   cd frontend && npm test
   ```

## Coding Standards

### Rust (Backend)

- **Follow Rust conventions** and use `cargo fmt`
- **Use meaningful names** for variables and functions
- **Write documentation** for public APIs
- **Handle errors properly** with Result types
- **Write unit tests** for business logic
- **Use structured logging** with appropriate levels

Example:
```rust
/// Creates a new form with the given configuration
pub async fn create_form(
    &self,
    form_data: CreateFormRequest,
    user_context: &UserContext,
) -> Result<Form, FormError> {
    // Implementation
}
```

### TypeScript (Frontend)

- **Use TypeScript strictly** with proper types
- **Follow React best practices** and hooks patterns
- **Use meaningful component names** in PascalCase
- **Write JSDoc comments** for complex functions
- **Use proper error boundaries**
- **Follow accessibility guidelines**

Example:
```typescript
interface FormBuilderProps {
  initialForm?: Form;
  onSave: (form: Form) => Promise<void>;
  onCancel: () => void;
}

export const FormBuilder: React.FC<FormBuilderProps> = ({
  initialForm,
  onSave,
  onCancel,
}) => {
  // Implementation
};
```

### Database

- **Use descriptive table and column names**
- **Include proper indexes** for performance
- **Write migration scripts** for schema changes
- **Document complex queries**
- **Follow normalization principles**

### Documentation

- **Update README** for significant changes
- **Document API changes** in the API documentation
- **Include code examples** where helpful
- **Keep documentation current** with code changes

## Testing Guidelines

### Backend Testing

- **Unit tests** for business logic
- **Integration tests** for API endpoints
- **Security tests** for authentication/authorization
- **Performance tests** for critical paths

```bash
# Run all tests
cargo test

# Run specific test
cargo test test_create_form

# Run with coverage
cargo tarpaulin --out Html
```

### Frontend Testing

- **Component tests** with React Testing Library
- **Integration tests** for user workflows
- **Accessibility tests** with axe-core
- **Visual regression tests** for UI components

```bash
# Run all tests
npm test

# Run specific test
npm test FormBuilder

# Run with coverage
npm test -- --coverage
```

### End-to-End Testing

- **Critical user journeys** should have E2E tests
- **Use realistic test data**
- **Test across different browsers**
- **Include mobile testing**

## Security Guidelines

### Code Security

- **Never commit secrets** or credentials
- **Validate all inputs** on both client and server
- **Use parameterized queries** to prevent SQL injection
- **Implement proper authentication** and authorization
- **Follow OWASP guidelines**

### Dependency Security

- **Keep dependencies updated**
- **Review security advisories**
- **Use `cargo audit` for Rust dependencies**
- **Use `npm audit` for Node.js dependencies**

## Performance Guidelines

### Backend Performance

- **Use database indexes** appropriately
- **Implement caching** where beneficial
- **Optimize database queries**
- **Use connection pooling**
- **Monitor memory usage**

### Frontend Performance

- **Optimize bundle size** with code splitting
- **Use lazy loading** for components
- **Implement proper caching**
- **Optimize images** and assets
- **Monitor Core Web Vitals**

## Git Workflow

### Branch Naming

- `feature/description` - New features
- `bugfix/description` - Bug fixes
- `hotfix/description` - Critical fixes
- `docs/description` - Documentation updates

### Commit Messages

Follow conventional commits format:

```
type(scope): description

[optional body]

[optional footer]
```

Types:
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation
- `style`: Code style changes
- `refactor`: Code refactoring
- `test`: Adding tests
- `chore`: Maintenance tasks

Examples:
```
feat(auth): add multi-factor authentication support

fix(forms): resolve validation error on empty fields

docs(api): update authentication endpoint documentation
```

### Pull Request Process

1. **Create feature branch** from `main`
2. **Make your changes** with proper commits
3. **Write/update tests** as needed
4. **Update documentation** if required
5. **Run all tests** and ensure they pass
6. **Submit pull request** with:
   - Clear title and description
   - Link to related issues
   - Screenshots for UI changes
   - Testing instructions

### Review Process

- **All PRs require review** from maintainers
- **Address feedback** promptly and professionally
- **Keep PRs focused** and reasonably sized
- **Rebase and squash** commits when requested

## Release Process

1. **Version bumping** follows semantic versioning
2. **Changelog updates** for each release
3. **Testing** in staging environment
4. **Security review** for major releases
5. **Documentation updates** as needed

## Getting Help

- **GitHub Discussions** for general questions
- **GitHub Issues** for bugs and feature requests
- **Documentation** for usage guidelines
- **Code comments** for implementation details

## Recognition

Contributors will be recognized in:
- **CONTRIBUTORS.md** file
- **Release notes** for significant contributions
- **GitHub contributors** section

Thank you for contributing to FORMS System! 🚀
