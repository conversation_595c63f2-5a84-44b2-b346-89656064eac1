[package]
name = "forms-backend"
version = "0.1.0"
edition = "2021"
authors = ["FORMS Team"]
description = "Enterprise-grade business management system backend"

[dependencies]
# Web Framework
axum = { version = "0.7", features = ["multipart", "ws", "macros"] }
tower = { version = "0.4", features = ["full"] }
tower-http = { version = "0.4", features = ["cors", "trace", "fs", "compression-gzip"] }
hyper = { version = "1.0", features = ["full"] }

# Async Runtime
tokio = { version = "1.0", features = ["full"] }
tokio-util = { version = "0.7", features = ["io"] }

# Database
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "postgres", "uuid", "chrono", "json"] }
sea-orm = { version = "0.12", features = ["sqlx-postgres", "runtime-tokio-rustls", "macros"] }
sea-orm-migration = "0.12"

# Redis
redis = { version = "0.23", features = ["tokio-comp", "connection-manager"] }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Authentication & Security
jsonwebtoken = "9.0"
argon2 = "0.5"

# Validation
validator = { version = "0.16", features = ["derive"] }

# UUID
uuid = { version = "1.0", features = ["v4", "serde"] }

# Date/Time
chrono = { version = "0.4", features = ["serde"] }

# Configuration
config = "0.13"
dotenvy = "0.15"

# Logging & Tracing
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
tracing-appender = "0.2"

# Error Handling
anyhow = "1.0"
thiserror = "1.0"

# File Handling
mime = "0.3"
mime_guess = "2.0"

# Utilities
once_cell = "1.19"
futures = "0.3"
rust_decimal = { version = "1.33", features = ["serde-with-str"] }
async-trait = "0.1"
rand = "0.8"
libc = "0.2"

[dev-dependencies]
tokio-test = "0.4"
