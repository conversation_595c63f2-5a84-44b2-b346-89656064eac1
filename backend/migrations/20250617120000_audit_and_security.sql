-- Audit and Security Features Migration
-- This migration adds comprehensive audit logging, security features, and monitoring capabilities

-- <PERSON><PERSON> logs table for tracking all user actions and system events
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id),
    impersonated_by UUID REFERENCES users(id),
    action VARCHAR(100) NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    entity_id UUID NOT NULL,
    old_values JSONB,
    new_values JSONB,
    changes JSONB, -- Specific field changes
    request_id VARCHAR(100), -- Correlation ID
    session_id VARCHAR(100),
    ip_address INET,
    user_agent TEXT,
    route VARCHAR(255),
    method VARCHAR(10),
    status_code INTEGER,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for audit logs performance
CREATE INDEX idx_audit_logs_tenant_id ON audit_logs(tenant_id);
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_entity ON audit_logs(entity_type, entity_id);
CREATE INDEX idx_audit_logs_created_date ON audit_logs(created_date);
CREATE INDEX idx_audit_logs_action ON audit_logs(action);
CREATE INDEX idx_audit_logs_tenant_date ON audit_logs(tenant_id, created_date DESC);

-- System logs table for application logging
CREATE TABLE system_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    level VARCHAR(20) NOT NULL, -- 'DEBUG', 'INFO', 'WARN', 'ERROR', 'FATAL'
    message TEXT NOT NULL,
    context JSONB,
    trace_id VARCHAR(100),
    span_id VARCHAR(100),
    source VARCHAR(100), -- Application component
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for system logs
CREATE INDEX idx_system_logs_level ON system_logs(level);
CREATE INDEX idx_system_logs_created_date ON system_logs(created_date);
CREATE INDEX idx_system_logs_trace_id ON system_logs(trace_id);

-- User sessions table for session management and security
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    refresh_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    refresh_expires_at TIMESTAMP NOT NULL,
    ip_address INET,
    user_agent TEXT,
    device_info JSONB,
    is_active BOOLEAN DEFAULT true,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for user sessions
CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_user_sessions_tenant_id ON user_sessions(tenant_id);
CREATE INDEX idx_user_sessions_session_token ON user_sessions(session_token);
CREATE INDEX idx_user_sessions_expires_at ON user_sessions(expires_at);

-- File access logs table for tracking file downloads and access
CREATE TABLE file_access_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    attachment_id UUID NOT NULL, -- Will reference attachments table when created
    user_id UUID REFERENCES users(id),
    action VARCHAR(50) NOT NULL, -- 'download', 'view', 'upload', 'delete'
    ip_address INET,
    user_agent TEXT,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for file access logs
CREATE INDEX idx_file_access_logs_attachment_id ON file_access_logs(attachment_id);
CREATE INDEX idx_file_access_logs_user_id ON file_access_logs(user_id);
CREATE INDEX idx_file_access_logs_created_date ON file_access_logs(created_date);

-- Security configurations table for storing security settings
CREATE TABLE security_configurations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    config_key VARCHAR(100) NOT NULL,
    config_value JSONB NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_user_id UUID,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_user_id UUID,
    UNIQUE(tenant_id, config_key)
);

-- Indexes for security configurations
CREATE INDEX idx_security_configurations_tenant_id ON security_configurations(tenant_id);
CREATE INDEX idx_security_configurations_key ON security_configurations(config_key);

-- Rate limiting tracking table (for persistent rate limiting)
CREATE TABLE rate_limit_tracking (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    key_hash VARCHAR(64) NOT NULL, -- Hash of the rate limit key (IP, user, etc.)
    key_type VARCHAR(50) NOT NULL, -- 'ip', 'user', 'api_key', etc.
    request_count INTEGER DEFAULT 0,
    window_start TIMESTAMP NOT NULL,
    window_duration_seconds INTEGER NOT NULL,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(key_hash, key_type)
);

-- Indexes for rate limiting
CREATE INDEX idx_rate_limit_tracking_key_hash ON rate_limit_tracking(key_hash);
CREATE INDEX idx_rate_limit_tracking_window_start ON rate_limit_tracking(window_start);

-- Failed login attempts tracking for security
CREATE TABLE failed_login_attempts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    identifier VARCHAR(255) NOT NULL, -- Email, username, or IP
    identifier_type VARCHAR(20) NOT NULL, -- 'email', 'username', 'ip'
    attempt_count INTEGER DEFAULT 1,
    last_attempt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    blocked_until TIMESTAMP,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for failed login attempts
CREATE INDEX idx_failed_login_attempts_identifier ON failed_login_attempts(identifier, identifier_type);
CREATE INDEX idx_failed_login_attempts_last_attempt ON failed_login_attempts(last_attempt);

-- Security incidents table for tracking security events
CREATE TABLE security_incidents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    incident_type VARCHAR(50) NOT NULL, -- 'brute_force', 'suspicious_activity', 'malware', etc.
    severity VARCHAR(20) NOT NULL, -- 'low', 'medium', 'high', 'critical'
    source_ip INET,
    user_id UUID REFERENCES users(id),
    tenant_id UUID REFERENCES tenants(id),
    description TEXT NOT NULL,
    details JSONB,
    status VARCHAR(20) DEFAULT 'open', -- 'open', 'investigating', 'resolved', 'false_positive'
    resolved_at TIMESTAMP,
    resolved_by UUID REFERENCES users(id),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for security incidents
CREATE INDEX idx_security_incidents_type ON security_incidents(incident_type);
CREATE INDEX idx_security_incidents_severity ON security_incidents(severity);
CREATE INDEX idx_security_incidents_status ON security_incidents(status);
CREATE INDEX idx_security_incidents_source_ip ON security_incidents(source_ip);
CREATE INDEX idx_security_incidents_created_date ON security_incidents(created_date);

-- API keys table for API access management
CREATE TABLE api_keys (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id),
    name VARCHAR(100) NOT NULL,
    key_hash VARCHAR(255) NOT NULL UNIQUE,
    key_prefix VARCHAR(20) NOT NULL, -- First few characters for identification
    permissions JSONB DEFAULT '[]', -- Array of permission strings
    rate_limit_override JSONB, -- Custom rate limits for this key
    last_used_at TIMESTAMP,
    expires_at TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_user_id UUID,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modified_user_id UUID
);

-- Indexes for API keys
CREATE INDEX idx_api_keys_tenant_id ON api_keys(tenant_id);
CREATE INDEX idx_api_keys_user_id ON api_keys(user_id);
CREATE INDEX idx_api_keys_key_hash ON api_keys(key_hash);
CREATE INDEX idx_api_keys_is_active ON api_keys(is_active);

-- Performance monitoring table
CREATE TABLE performance_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(15,6) NOT NULL,
    metric_unit VARCHAR(20), -- 'ms', 'bytes', 'count', etc.
    tags JSONB, -- Additional metadata
    tenant_id UUID REFERENCES tenants(id),
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for performance metrics
CREATE INDEX idx_performance_metrics_name ON performance_metrics(metric_name);
CREATE INDEX idx_performance_metrics_recorded_at ON performance_metrics(recorded_at);
CREATE INDEX idx_performance_metrics_tenant_id ON performance_metrics(tenant_id);

-- Create a function to automatically clean up old audit logs
CREATE OR REPLACE FUNCTION cleanup_old_audit_logs(retention_days INTEGER DEFAULT 90)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM audit_logs 
    WHERE created_date < NOW() - INTERVAL '1 day' * retention_days;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Log the cleanup operation
    INSERT INTO system_logs (level, message, context, source)
    VALUES (
        'INFO',
        'Audit logs cleanup completed',
        jsonb_build_object(
            'deleted_count', deleted_count,
            'retention_days', retention_days
        ),
        'audit_cleanup'
    );
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Create a function to automatically clean up old system logs
CREATE OR REPLACE FUNCTION cleanup_old_system_logs(retention_days INTEGER DEFAULT 30)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM system_logs 
    WHERE created_date < NOW() - INTERVAL '1 day' * retention_days
    AND level NOT IN ('ERROR', 'FATAL'); -- Keep error logs longer
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Create a function to get audit statistics
CREATE OR REPLACE FUNCTION get_audit_statistics(
    p_tenant_id UUID DEFAULT NULL,
    p_date_from TIMESTAMP DEFAULT NOW() - INTERVAL '30 days',
    p_date_to TIMESTAMP DEFAULT NOW()
)
RETURNS TABLE (
    total_actions BIGINT,
    unique_users BIGINT,
    failed_actions BIGINT,
    impersonated_actions BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_actions,
        COUNT(DISTINCT user_id) as unique_users,
        COUNT(*) FILTER (WHERE status_code >= 400) as failed_actions,
        COUNT(*) FILTER (WHERE impersonated_by IS NOT NULL) as impersonated_actions
    FROM audit_logs
    WHERE created_date >= p_date_from 
    AND created_date <= p_date_to
    AND (p_tenant_id IS NULL OR tenant_id = p_tenant_id);
END;
$$ LANGUAGE plpgsql;

-- Insert default system permissions for audit and security
INSERT INTO permissions (name, resource, action, description, is_system_permission) VALUES
('audit:read', 'audit', 'read', 'View audit logs', true),
('audit:statistics', 'audit', 'statistics', 'View audit statistics', true),
('audit:cleanup', 'audit', 'cleanup', 'Clean up old audit logs', true),
('security:read', 'security', 'read', 'View security configurations', true),
('security:write', 'security', 'write', 'Modify security configurations', true),
('security:incidents', 'security', 'incidents', 'Manage security incidents', true),
('api_keys:read', 'api_keys', 'read', 'View API keys', true),
('api_keys:write', 'api_keys', 'write', 'Manage API keys', true),
('sessions:read', 'sessions', 'read', 'View user sessions', true),
('sessions:revoke', 'sessions', 'revoke', 'Revoke user sessions', true);
