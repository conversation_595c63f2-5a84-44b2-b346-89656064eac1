use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum FormEvent {
    FormCreated(FormCreatedEvent),
    FormUpdated(FormUpdatedEvent),
    FormDeleted(FormDeletedEvent),
    FormStatusChanged(FormStatusChangedEvent),
    FormLocked(FormLockedEvent),
    FormUnlocked(FormUnlockedEvent),
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct FormCreatedEvent {
    pub form_id: Uuid,
    pub tenant_id: Uuid,
    pub form_number: String,
    pub created_by: Option<Uuid>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct FormUpdatedEvent {
    pub form_id: Uuid,
    pub updated_by: Option<Uuid>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct FormDeletedEvent {
    pub form_id: Uuid,
    pub deleted_by: Option<Uuid>,
    pub deleted_at: DateTime<Utc>,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct FormStatusChangedEvent {
    pub form_id: Uuid,
    pub old_status: String,
    pub new_status: String,
    pub changed_by: Option<Uuid>,
    pub changed_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FormLockedEvent {
    pub form_id: Uuid,
    pub locked_by: Uuid,
    pub locked_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FormUnlockedEvent {
    pub form_id: Uuid,
    pub unlocked_by: Uuid,
    pub unlocked_at: DateTime<Utc>,
}
