use async_trait::async_trait;
use uuid::Uuid;

use crate::domain::entities::{Attachment, CreateAttachmentRequest};
use crate::domain::value_objects::{PaginationRequest, Pagination, SearchQuery};

#[async_trait]
pub trait AttachmentRepository: Send + Sync {
    async fn create(&self, attachment: Attachment) -> Result<Attachment, Box<dyn std::error::Error>>;
    async fn find_by_id(&self, id: Uuid) -> Result<Option<Attachment>, Box<dyn std::error::Error>>;
    async fn update(&self, attachment: Attachment) -> Result<Attachment, Box<dyn std::error::Error>>;
    async fn delete(&self, id: Uuid) -> Result<(), Box<dyn std::error::Error>>;
    async fn find_by_entity(
        &self,
        entity_type: &str,
        entity_id: Uuid,
    ) -> Result<Vec<Attachment>, Box<dyn std::error::Error>>;
    async fn find_by_hash(&self, file_hash: &str) -> Result<Option<Attachment>, Box<dyn std::error::Error>>;
    async fn increment_download_count(&self, id: Uuid) -> Result<(), Box<dyn std::error::Error>>;
}
