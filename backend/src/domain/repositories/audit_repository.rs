use async_trait::async_trait;
use chrono::{DateTime, Utc};
use uuid::Uuid;

use crate::domain::entities::AuditLog;
use crate::domain::value_objects::{PaginationRequest, Pagination, SearchQuery};

#[derive(Debug, Clone)]
pub struct CreateAuditLogRequest {
    pub tenant_id: Option<Uuid>,
    pub user_id: Option<Uuid>,
    pub impersonated_by: Option<Uuid>,
    pub action: String,
    pub entity_type: String,
    pub entity_id: Uuid,
    pub old_values: Option<serde_json::Value>,
    pub new_values: Option<serde_json::Value>,
    pub changes: Option<serde_json::Value>,
    pub request_id: Option<String>,
    pub session_id: Option<String>,
    pub ip_address: Option<String>,
    pub user_agent: Option<String>,
    pub route: Option<String>,
    pub method: Option<String>,
    pub status_code: Option<i32>,
}

#[derive(Debug, Clone)]
pub struct AuditLogFilter {
    pub tenant_id: Option<Uuid>,
    pub user_id: Option<Uuid>,
    pub entity_type: Option<String>,
    pub entity_id: Option<Uuid>,
    pub action: Option<String>,
    pub date_from: Option<DateTime<Utc>>,
    pub date_to: Option<DateTime<Utc>>,
    pub impersonated_by: Option<Uuid>,
}

#[async_trait]
pub trait AuditRepository: Send + Sync {
    /// Create a new audit log entry
    async fn create(&self, request: CreateAuditLogRequest) -> Result<AuditLog, Box<dyn std::error::Error>>;
    
    /// Find audit log by ID
    async fn find_by_id(&self, id: Uuid) -> Result<Option<AuditLog>, Box<dyn std::error::Error>>;
    
    /// List audit logs with filtering and pagination
    async fn list(
        &self,
        filter: AuditLogFilter,
        pagination: PaginationRequest,
        search: SearchQuery,
    ) -> Result<(Vec<AuditLog>, Pagination), Box<dyn std::error::Error>>;
    
    /// Get audit logs for a specific entity
    async fn find_by_entity(
        &self,
        entity_type: &str,
        entity_id: Uuid,
        pagination: PaginationRequest,
    ) -> Result<(Vec<AuditLog>, Pagination), Box<dyn std::error::Error>>;
    
    /// Get audit logs for a specific user
    async fn find_by_user(
        &self,
        user_id: Uuid,
        pagination: PaginationRequest,
    ) -> Result<(Vec<AuditLog>, Pagination), Box<dyn std::error::Error>>;
    
    /// Get audit logs for a specific tenant
    async fn find_by_tenant(
        &self,
        tenant_id: Uuid,
        pagination: PaginationRequest,
    ) -> Result<(Vec<AuditLog>, Pagination), Box<dyn std::error::Error>>;
    
    /// Delete old audit logs (for data retention)
    async fn delete_older_than(&self, date: DateTime<Utc>) -> Result<u64, Box<dyn std::error::Error>>;
    
    /// Get audit statistics
    async fn get_statistics(
        &self,
        tenant_id: Option<Uuid>,
        date_from: DateTime<Utc>,
        date_to: DateTime<Utc>,
    ) -> Result<AuditStatistics, Box<dyn std::error::Error>>;
}

#[derive(Debug, Clone)]
pub struct AuditStatistics {
    pub total_actions: u64,
    pub unique_users: u64,
    pub actions_by_type: std::collections::HashMap<String, u64>,
    pub actions_by_entity: std::collections::HashMap<String, u64>,
    pub failed_actions: u64,
    pub impersonated_actions: u64,
}
