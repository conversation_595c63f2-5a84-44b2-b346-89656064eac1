use async_trait::async_trait;
use uuid::Uuid;

use crate::domain::entities::{Company, CreateCompanyRequest, UpdateCompanyRequest};
use crate::domain::value_objects::{PaginationRequest, Pagination, SearchQuery};

#[async_trait]
pub trait CompanyRepository: Send + Sync {
    async fn create(&self, company: Company) -> Result<Company, Box<dyn std::error::Error>>;
    async fn find_by_id(&self, id: Uuid) -> Result<Option<Company>, Box<dyn std::error::Error>>;
    async fn update(&self, company: Company) -> Result<Company, Box<dyn std::error::Error>>;
    async fn delete(&self, id: Uuid) -> Result<(), Box<dyn std::error::Error>>;
    async fn list_by_tenant(
        &self,
        tenant_id: Uuid,
        pagination: PaginationRequest,
        search: SearchQuery,
    ) -> Result<(Vec<Company>, Pagination), Box<dyn std::error::Error>>;
    async fn find_by_type(&self, tenant_id: Uuid, company_type: &str) -> Result<Vec<Company>, Box<dyn std::error::Error>>;
}
