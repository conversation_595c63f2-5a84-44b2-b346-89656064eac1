use async_trait::async_trait;
use uuid::Uuid;

use crate::domain::entities::{Contact, CreateContactRequest, UpdateContactRequest};
use crate::domain::value_objects::{PaginationRequest, Pagination, SearchQuery};

#[async_trait]
pub trait ContactRepository: Send + Sync {
    async fn create(&self, contact: Contact) -> Result<Contact, Box<dyn std::error::Error>>;
    async fn find_by_id(&self, id: Uuid) -> Result<Option<Contact>, Box<dyn std::error::Error>>;
    async fn update(&self, contact: Contact) -> Result<Contact, Box<dyn std::error::Error>>;
    async fn delete(&self, id: Uuid) -> Result<(), Box<dyn std::error::Error>>;
    async fn list_by_tenant(
        &self,
        tenant_id: Uuid,
        pagination: PaginationRequest,
        search: SearchQuery,
    ) -> Result<(Vec<Contact>, Pagination), Box<dyn std::error::Error>>;
    async fn find_by_company(&self, company_id: Uuid) -> Result<Vec<Contact>, Box<dyn std::error::Error>>;
    async fn find_primary_by_company(&self, company_id: Uuid) -> Result<Option<Contact>, Box<dyn std::error::Error>>;
}
