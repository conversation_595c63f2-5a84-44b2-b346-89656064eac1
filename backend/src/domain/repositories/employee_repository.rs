use async_trait::async_trait;
use uuid::Uuid;

use crate::domain::entities::{Employee, CreateEmployeeRequest, UpdateEmployeeRequest};
use crate::domain::value_objects::{PaginationRequest, Pagination, SearchQuery};

#[async_trait]
pub trait EmployeeRepository: Send + Sync {
    async fn create(&self, employee: Employee) -> Result<Employee, Box<dyn std::error::Error>>;
    async fn find_by_id(&self, id: Uuid) -> Result<Option<Employee>, Box<dyn std::error::Error>>;
    async fn find_by_employee_code(&self, tenant_id: Uuid, employee_code: &str) -> Result<Option<Employee>, Box<dyn std::error::Error>>;
    async fn find_by_user_id(&self, user_id: Uuid) -> Result<Option<Employee>, Box<dyn std::error::Error>>;
    async fn update(&self, employee: Employee) -> Result<Employee, Box<dyn std::error::Error>>;
    async fn delete(&self, id: Uuid) -> Result<(), Box<dyn std::error::Error>>;
    async fn list_by_tenant(
        &self,
        tenant_id: Uuid,
        pagination: PaginationRequest,
        search: SearchQuery,
    ) -> Result<(Vec<Employee>, Pagination), Box<dyn std::error::Error>>;
    async fn find_by_department(&self, department_id: Uuid) -> Result<Vec<Employee>, Box<dyn std::error::Error>>;
    async fn find_by_manager(&self, manager_id: Uuid) -> Result<Vec<Employee>, Box<dyn std::error::Error>>;
}
