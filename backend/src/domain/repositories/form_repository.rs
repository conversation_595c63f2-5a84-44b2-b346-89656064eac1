use async_trait::async_trait;
use uuid::Uuid;

use crate::domain::entities::{Form, CreateFormRequest, UpdateFormRequest, FormItem};
use crate::domain::value_objects::{PaginationRequest, Pagination, SearchQuery};

#[async_trait]
pub trait FormRepository: Send + Sync {
    async fn create(&self, form: Form) -> Result<Form, Box<dyn std::error::Error>>;
    async fn find_by_id(&self, id: Uuid) -> Result<Option<Form>, Box<dyn std::error::Error>>;
    async fn find_by_form_number(&self, tenant_id: Uuid, form_number: &str) -> Result<Option<Form>, Box<dyn std::error::Error>>;
    async fn update(&self, form: Form) -> Result<Form, Box<dyn std::error::Error>>;
    async fn delete(&self, id: Uuid) -> Result<(), Box<dyn std::error::Error>>;
    async fn list_by_tenant(
        &self,
        tenant_id: Uuid,
        pagination: PaginationRequest,
        search: SearchQuery,
    ) -> Result<(Vec<Form>, Pagination), Box<dyn std::error::Error>>;
    async fn find_by_status(&self, tenant_id: Uuid, status: &str) -> Result<Vec<Form>, Box<dyn std::error::Error>>;
    async fn find_by_company(&self, company_id: Uuid) -> Result<Vec<Form>, Box<dyn std::error::Error>>;
    async fn find_by_employee(&self, employee_id: Uuid) -> Result<Vec<Form>, Box<dyn std::error::Error>>;
    async fn lock_form(&self, id: Uuid, locked_by: Uuid) -> Result<(), Box<dyn std::error::Error>>;
    async fn unlock_form(&self, id: Uuid) -> Result<(), Box<dyn std::error::Error>>;
}
