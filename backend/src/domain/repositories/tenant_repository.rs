use async_trait::async_trait;
use uuid::Uuid;

use crate::domain::entities::{Tenant, CreateTenantRequest, UpdateTenantRequest};
use crate::domain::value_objects::{PaginationRequest, Pagination, SearchQuery};

#[async_trait]
pub trait TenantRepository: Send + Sync {
    async fn create(&self, tenant: Tenant) -> Result<Tenant, Box<dyn std::error::Error>>;
    async fn find_by_id(&self, id: Uuid) -> Result<Option<Tenant>, Box<dyn std::error::Error>>;
    async fn find_by_slug(&self, slug: &str) -> Result<Option<Tenant>, Box<dyn std::error::Error>>;
    async fn find_by_domain(&self, domain: &str) -> Result<Option<Tenant>, Box<dyn std::error::Error>>;
    async fn update(&self, tenant: Tenant) -> Result<Tenant, Box<dyn std::error::Error>>;
    async fn delete(&self, id: Uuid) -> Result<(), Box<dyn std::error::Error>>;
    async fn list(
        &self,
        pagination: PaginationRequest,
        search: SearchQuery,
    ) -> Result<(Vec<Tenant>, Pagination), Box<dyn std::error::Error>>;
    async fn activate(&self, id: Uuid) -> Result<(), Box<dyn std::error::Error>>;
    async fn deactivate(&self, id: Uuid) -> Result<(), Box<dyn std::error::Error>>;
}
