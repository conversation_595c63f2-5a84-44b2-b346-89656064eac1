use std::sync::Arc;
use chrono::{DateTime, Utc};
use serde_json::Value as JsonValue;
use uuid::Uuid;

use crate::domain::entities::AuditLog;
use crate::domain::repositories::{AuditRepository, CreateAuditLogRequest, AuditLogFilter, AuditStatistics};
use crate::domain::value_objects::{PaginationRequest, Pagination, SearchQuery};

pub struct AuditService {
    audit_repository: Arc<dyn AuditRepository>,
}

impl AuditService {
    pub fn new(audit_repository: Arc<dyn AuditRepository>) -> Self {
        Self {
            audit_repository,
        }
    }

    /// Log a user action with full context
    pub async fn log_action(
        &self,
        tenant_id: Option<Uuid>,
        user_id: Option<Uuid>,
        action: &str,
        entity_type: &str,
        entity_id: Uuid,
        old_values: Option<JsonValue>,
        new_values: Option<JsonValue>,
        request_context: Option<RequestContext>,
    ) -> Result<AuditLog, Box<dyn std::error::Error>> {
        let changes = self.calculate_changes(&old_values, &new_values);
        
        let request = CreateAuditLogRequest {
            tenant_id,
            user_id,
            impersonated_by: request_context.as_ref().and_then(|ctx| ctx.impersonated_by),
            action: action.to_string(),
            entity_type: entity_type.to_string(),
            entity_id,
            old_values,
            new_values,
            changes,
            request_id: request_context.as_ref().map(|ctx| ctx.request_id.clone()),
            session_id: request_context.as_ref().map(|ctx| ctx.session_id.clone()),
            ip_address: request_context.as_ref().map(|ctx| ctx.ip_address.clone()),
            user_agent: request_context.as_ref().map(|ctx| ctx.user_agent.clone()),
            route: request_context.as_ref().map(|ctx| ctx.route.clone()),
            method: request_context.as_ref().map(|ctx| ctx.method.clone()),
            status_code: request_context.as_ref().and_then(|ctx| ctx.status_code),
        };

        self.audit_repository.create(request).await
    }

    /// Log a simple action without old/new values
    pub async fn log_simple_action(
        &self,
        tenant_id: Option<Uuid>,
        user_id: Option<Uuid>,
        action: &str,
        entity_type: &str,
        entity_id: Uuid,
        request_context: Option<RequestContext>,
    ) -> Result<AuditLog, Box<dyn std::error::Error>> {
        self.log_action(
            tenant_id,
            user_id,
            action,
            entity_type,
            entity_id,
            None,
            None,
            request_context,
        ).await
    }

    /// Log authentication events
    pub async fn log_auth_event(
        &self,
        user_id: Option<Uuid>,
        action: &str,
        success: bool,
        request_context: Option<RequestContext>,
    ) -> Result<AuditLog, Box<dyn std::error::Error>> {
        let entity_id = user_id.unwrap_or_else(|| Uuid::new_v4());
        let action_with_result = if success {
            format!("{}_success", action)
        } else {
            format!("{}_failure", action)
        };

        self.log_simple_action(
            None, // Auth events are not tenant-specific
            user_id,
            &action_with_result,
            "authentication",
            entity_id,
            request_context,
        ).await
    }

    /// Get audit logs with filtering
    pub async fn get_audit_logs(
        &self,
        filter: AuditLogFilter,
        pagination: PaginationRequest,
        search: SearchQuery,
    ) -> Result<(Vec<AuditLog>, Pagination), Box<dyn std::error::Error>> {
        self.audit_repository.list(filter, pagination, search).await
    }

    /// Get audit logs for a specific entity
    pub async fn get_entity_audit_logs(
        &self,
        entity_type: &str,
        entity_id: Uuid,
        pagination: PaginationRequest,
    ) -> Result<(Vec<AuditLog>, Pagination), Box<dyn std::error::Error>> {
        self.audit_repository.find_by_entity(entity_type, entity_id, pagination).await
    }

    /// Get audit statistics
    pub async fn get_statistics(
        &self,
        tenant_id: Option<Uuid>,
        date_from: DateTime<Utc>,
        date_to: DateTime<Utc>,
    ) -> Result<AuditStatistics, Box<dyn std::error::Error>> {
        self.audit_repository.get_statistics(tenant_id, date_from, date_to).await
    }

    /// Clean up old audit logs based on retention policy
    pub async fn cleanup_old_logs(
        &self,
        retention_days: u32,
    ) -> Result<u64, Box<dyn std::error::Error>> {
        let cutoff_date = Utc::now() - chrono::Duration::days(retention_days as i64);
        self.audit_repository.delete_older_than(cutoff_date).await
    }

    /// Calculate changes between old and new values
    fn calculate_changes(&self, old_values: &Option<JsonValue>, new_values: &Option<JsonValue>) -> Option<JsonValue> {
        match (old_values, new_values) {
            (Some(old), Some(new)) => {
                let mut changes = serde_json::Map::new();
                
                if let (JsonValue::Object(old_obj), JsonValue::Object(new_obj)) = (old, new) {
                    // Find changed fields
                    for (key, new_value) in new_obj {
                        if let Some(old_value) = old_obj.get(key) {
                            if old_value != new_value {
                                changes.insert(key.clone(), serde_json::json!({
                                    "from": old_value,
                                    "to": new_value
                                }));
                            }
                        } else {
                            // New field added
                            changes.insert(key.clone(), serde_json::json!({
                                "from": null,
                                "to": new_value
                            }));
                        }
                    }
                    
                    // Find removed fields
                    for (key, old_value) in old_obj {
                        if !new_obj.contains_key(key) {
                            changes.insert(key.clone(), serde_json::json!({
                                "from": old_value,
                                "to": null
                            }));
                        }
                    }
                }
                
                if changes.is_empty() {
                    None
                } else {
                    Some(JsonValue::Object(changes))
                }
            }
            _ => None,
        }
    }
}

#[derive(Debug, Clone)]
pub struct RequestContext {
    pub request_id: String,
    pub session_id: String,
    pub ip_address: String,
    pub user_agent: String,
    pub route: String,
    pub method: String,
    pub status_code: Option<i32>,
    pub impersonated_by: Option<Uuid>,
}

impl RequestContext {
    pub fn new(
        request_id: String,
        session_id: String,
        ip_address: String,
        user_agent: String,
        route: String,
        method: String,
    ) -> Self {
        Self {
            request_id,
            session_id,
            ip_address,
            user_agent,
            route,
            method,
            status_code: None,
            impersonated_by: None,
        }
    }

    pub fn with_status_code(mut self, status_code: i32) -> Self {
        self.status_code = Some(status_code);
        self
    }

    pub fn with_impersonation(mut self, impersonated_by: Uuid) -> Self {
        self.impersonated_by = Some(impersonated_by);
        self
    }
}

// Common audit actions
pub mod actions {
    pub const CREATE: &str = "create";
    pub const UPDATE: &str = "update";
    pub const DELETE: &str = "delete";
    pub const VIEW: &str = "view";
    pub const LOGIN: &str = "login";
    pub const LOGOUT: &str = "logout";
    pub const REGISTER: &str = "register";
    pub const PASSWORD_CHANGE: &str = "password_change";
    pub const IMPERSONATE_START: &str = "impersonate_start";
    pub const IMPERSONATE_END: &str = "impersonate_end";
    pub const EXPORT: &str = "export";
    pub const IMPORT: &str = "import";
    pub const UPLOAD: &str = "upload";
    pub const DOWNLOAD: &str = "download";
    pub const APPROVE: &str = "approve";
    pub const REJECT: &str = "reject";
    pub const SUBMIT: &str = "submit";
    pub const CANCEL: &str = "cancel";
}

// Common entity types
pub mod entities {
    pub const USER: &str = "user";
    pub const TENANT: &str = "tenant";
    pub const EMPLOYEE: &str = "employee";
    pub const COMPANY: &str = "company";
    pub const CONTACT: &str = "contact";
    pub const FORM: &str = "form";
    pub const FORM_ITEM: &str = "form_item";
    pub const ATTACHMENT: &str = "attachment";
    pub const ROLE: &str = "role";
    pub const PERMISSION: &str = "permission";
    pub const WORKFLOW: &str = "workflow";
    pub const AUTHENTICATION: &str = "authentication";
    pub const SYSTEM: &str = "system";
}
