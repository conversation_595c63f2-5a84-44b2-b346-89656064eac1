use uuid::Uuid;
use chrono::{DateTime, Utc};

use crate::domain::entities::User;
use crate::domain::repositories::UserRepository;

pub struct AuthService {
    user_repository: Box<dyn UserRepository>,
}

#[derive(<PERSON>bu<PERSON>, <PERSON>lone)]
pub struct LoginRequest {
    pub email_or_username: String,
    pub password: String,
}

#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct LoginResult {
    pub user: User,
    pub access_token: String,
    pub refresh_token: String,
    pub expires_at: DateTime<Utc>,
}

impl AuthService {
    pub fn new(user_repository: Box<dyn UserRepository>) -> Self {
        Self { user_repository }
    }

    pub async fn login(&self, request: LoginRequest) -> Result<LoginResult, Box<dyn std::error::Error>> {
        // Find user by email or username
        let user = if request.email_or_username.contains('@') {
            self.user_repository.find_by_email(&request.email_or_username).await?
        } else {
            self.user_repository.find_by_username(&request.email_or_username).await?
        };

        let mut user = user.ok_or("Invalid credentials")?;

        // Check if user is active
        if !user.is_active {
            return Err("Account is deactivated".into());
        }

        // Check if user is locked
        if user.is_locked() {
            return Err("Account is locked".into());
        }

        // Verify password (this will be implemented in infrastructure layer)
        let password_valid = self.verify_password(&request.password, &user.password_hash).await?;
        
        if !password_valid {
            // Increment failed login attempts
            self.user_repository.increment_failed_login(user.id).await?;
            
            // Lock user if too many failed attempts
            if user.failed_login_attempts >= 4 {
                let locked_until = Utc::now() + chrono::Duration::minutes(30);
                self.user_repository.lock_user(user.id, locked_until).await?;
                return Err("Account locked due to too many failed login attempts".into());
            }
            
            return Err("Invalid credentials".into());
        }

        // Reset failed login attempts on successful login
        self.user_repository.reset_failed_login(user.id).await?;
        
        // Update last login
        self.user_repository.update_last_login(user.id).await?;

        // Generate tokens (this will be implemented in infrastructure layer)
        let access_token = self.generate_access_token(&user).await?;
        let refresh_token = self.generate_refresh_token(&user).await?;
        let expires_at = Utc::now() + chrono::Duration::hours(1);

        Ok(LoginResult {
            user,
            access_token,
            refresh_token,
            expires_at,
        })
    }

    pub async fn refresh_token(&self, refresh_token: &str) -> Result<LoginResult, Box<dyn std::error::Error>> {
        // Validate refresh token and get user (this will be implemented in infrastructure layer)
        let user_id = self.validate_refresh_token(refresh_token).await?;
        let user = self.user_repository.find_by_id(user_id).await?
            .ok_or("User not found")?;

        // Generate new tokens
        let access_token = self.generate_access_token(&user).await?;
        let new_refresh_token = self.generate_refresh_token(&user).await?;
        let expires_at = Utc::now() + chrono::Duration::hours(1);

        Ok(LoginResult {
            user,
            access_token,
            refresh_token: new_refresh_token,
            expires_at,
        })
    }

    pub async fn logout(&self, user_id: Uuid, token: &str) -> Result<(), Box<dyn std::error::Error>> {
        // Invalidate token (this will be implemented in infrastructure layer)
        self.invalidate_token(token).await?;
        Ok(())
    }

    // These methods will be implemented in the infrastructure layer
    async fn verify_password(&self, password: &str, hash: &str) -> Result<bool, Box<dyn std::error::Error>> {
        // Placeholder implementation
        Ok(format!("hashed_{}", password) == hash)
    }

    async fn generate_access_token(&self, user: &User) -> Result<String, Box<dyn std::error::Error>> {
        // Placeholder implementation
        Ok(format!("access_token_{}", user.id))
    }

    async fn generate_refresh_token(&self, user: &User) -> Result<String, Box<dyn std::error::Error>> {
        // Placeholder implementation
        Ok(format!("refresh_token_{}", user.id))
    }

    async fn validate_refresh_token(&self, token: &str) -> Result<Uuid, Box<dyn std::error::Error>> {
        // Placeholder implementation
        if token.starts_with("refresh_token_") {
            let id_str = token.strip_prefix("refresh_token_").unwrap();
            Ok(Uuid::parse_str(id_str)?)
        } else {
            Err("Invalid refresh token".into())
        }
    }

    async fn invalidate_token(&self, token: &str) -> Result<(), Box<dyn std::error::Error>> {
        // Placeholder implementation
        Ok(())
    }
}
