use std::sync::Arc;
use uuid::Uuid;

use crate::domain::entities::{Attachment, CreateAttachmentRequest};
use crate::domain::repositories::AttachmentRepository;
use crate::infrastructure::security::{FileSecurityService, FileValidationResult};
use crate::error::{AppError, AppResult};

pub struct FileService {
    attachment_repository: Box<dyn AttachmentRepository>,
    security_service: FileSecurityService,
}

impl FileService {
    pub fn new(
        attachment_repository: Box<dyn AttachmentRepository>,
        security_service: FileSecurityService,
    ) -> Self {
        Self {
            attachment_repository,
            security_service,
        }
    }

    pub async fn upload_file(
        &self,
        tenant_id: Uuid,
        request: CreateAttachmentRequest,
        file_content: Vec<u8>,
        uploaded_by: Option<Uuid>,
    ) -> AppResult<Attachment> {
        // Validate file security
        let validation_result = self.security_service
            .validate_file(&request.original_name, &file_content, Some(&request.mime_type))
            .await?;

        if !validation_result.is_valid {
            let issues: Vec<String> = validation_result.security_issues
                .iter()
                .map(|issue| issue.description.clone())
                .collect();
            return Err(AppError::file_error(format!("File validation failed: {}", issues.join(", "))));
        }

        // Check if file already exists (using security service hash)
        if let Some(existing) = self.attachment_repository.find_by_hash(&validation_result.file_hash).await? {
            return Ok(existing);
        }

        // Generate safe file name
        let safe_file_name = self.security_service.generate_safe_filename(&request.original_name);

        // Generate file path
        let file_path = self.generate_file_path(tenant_id, &request.entity_type, &safe_file_name);

        // Save file to storage
        self.save_file_to_storage(&file_path, file_content).await?;

        let attachment = Attachment::new(
            tenant_id,
            request,
            safe_file_name,
            file_path,
            Some(validation_result.file_hash),
            uploaded_by,
        );

        self.attachment_repository.create(attachment).await
            .map_err(|e| AppError::internal(format!("Failed to save attachment: {}", e)))
    }

    pub async fn download_file(&self, id: Uuid, user_id: Option<Uuid>) -> Result<(Attachment, Vec<u8>), Box<dyn std::error::Error>> {
        let mut attachment = self.attachment_repository.find_by_id(id).await?
            .ok_or("File not found")?;
        
        // Check permissions (simplified - in real implementation, check tenant access)
        if !attachment.is_public && user_id.is_none() {
            return Err("Access denied".into());
        }
        
        // Load file content from storage
        let file_content = self.load_file_from_storage(&attachment.file_path).await?;
        
        // Increment download count
        attachment.increment_download_count();
        self.attachment_repository.update(attachment.clone()).await?;
        
        Ok((attachment, file_content))
    }

    pub async fn delete_file(&self, id: Uuid, user_id: Uuid) -> Result<(), Box<dyn std::error::Error>> {
        let attachment = self.attachment_repository.find_by_id(id).await?
            .ok_or("File not found")?;
        
        // Check permissions (simplified)
        if attachment.uploaded_by != Some(user_id) {
            return Err("Access denied".into());
        }
        
        // Delete file from storage
        self.delete_file_from_storage(&attachment.file_path).await?;
        
        // Delete attachment record
        self.attachment_repository.delete(id).await
    }

    pub async fn list_files_by_entity(&self, entity_type: &str, entity_id: Uuid) -> Result<Vec<Attachment>, Box<dyn std::error::Error>> {
        self.attachment_repository.find_by_entity(entity_type, entity_id).await
    }

    // Helper methods that will be implemented in infrastructure layer
    fn calculate_file_hash(&self, content: &[u8]) -> String {
        // Placeholder implementation - use SHA-256 in real implementation
        format!("hash_{}", content.len())
    }

    fn extract_file_extension(&self, filename: &str) -> String {
        std::path::Path::new(filename)
            .extension()
            .and_then(|ext| ext.to_str())
            .map(|ext| format!(".{}", ext))
            .unwrap_or_default()
    }

    fn generate_file_path(&self, tenant_id: Uuid, entity_type: &str, file_name: &str) -> String {
        format!("{}/{}/{}", tenant_id, entity_type, file_name)
    }

    async fn save_file_to_storage(&self, _path: &str, _content: Vec<u8>) -> Result<(), Box<dyn std::error::Error>> {
        // Placeholder implementation
        Ok(())
    }

    async fn load_file_from_storage(&self, _path: &str) -> Result<Vec<u8>, Box<dyn std::error::Error>> {
        // Placeholder implementation
        Ok(vec![])
    }

    async fn delete_file_from_storage(&self, _path: &str) -> Result<(), Box<dyn std::error::Error>> {
        // Placeholder implementation
        Ok(())
    }

    /// Validate a file using the security service
    pub async fn validate_file(
        &self,
        filename: &str,
        content: &[u8],
        declared_mime_type: Option<&str>,
    ) -> AppResult<crate::infrastructure::security::FileValidationResult> {
        self.security_service.validate_file(filename, content, declared_mime_type).await
    }
}
