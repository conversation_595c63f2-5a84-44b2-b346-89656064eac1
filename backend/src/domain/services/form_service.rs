use uuid::Uuid;

use crate::domain::entities::{Form, CreateFormRequest, UpdateFormRequest};
use crate::domain::repositories::FormRepository;
use crate::domain::value_objects::{PaginationRequest, Pagination, SearchQuery};

pub struct FormService {
    form_repository: Box<dyn FormRepository>,
}

impl FormService {
    pub fn new(form_repository: Box<dyn FormRepository>) -> Self {
        Self { form_repository }
    }

    pub async fn create_form(&self, tenant_id: Uuid, request: CreateFormRequest, created_by: Option<Uuid>) -> Result<Form, Box<dyn std::error::Error>> {
        // Validate form number uniqueness within tenant
        if let Some(_) = self.form_repository.find_by_form_number(tenant_id, &request.form_number).await? {
            return Err("Form number already exists".into());
        }
        
        let form = Form::new(tenant_id, request, created_by);
        self.form_repository.create(form).await
    }

    pub async fn get_form(&self, id: Uuid) -> Result<Option<Form>, Box<dyn std::error::Error>> {
        self.form_repository.find_by_id(id).await
    }

    pub async fn update_form(&self, id: Uuid, request: UpdateFormRequest, modified_by: Option<Uuid>) -> Result<Form, Box<dyn std::error::Error>> {
        let mut form = self.form_repository.find_by_id(id).await?
            .ok_or("Form not found")?;
        
        // Check if form is editable
        if !form.is_editable() {
            return Err("Form is not editable in current state".into());
        }
        
        form.update(request, modified_by);
        self.form_repository.update(form).await
    }

    pub async fn delete_form(&self, id: Uuid) -> Result<(), Box<dyn std::error::Error>> {
        let form = self.form_repository.find_by_id(id).await?
            .ok_or("Form not found")?;
        
        // Only allow deletion of draft forms
        if form.status != "draft" {
            return Err("Only draft forms can be deleted".into());
        }
        
        self.form_repository.delete(id).await
    }

    pub async fn list_forms(&self, tenant_id: Uuid, pagination: PaginationRequest, search: SearchQuery) -> Result<(Vec<Form>, Pagination), Box<dyn std::error::Error>> {
        self.form_repository.list_by_tenant(tenant_id, pagination, search).await
    }

    pub async fn lock_form(&self, id: Uuid, locked_by: Uuid) -> Result<(), Box<dyn std::error::Error>> {
        let mut form = self.form_repository.find_by_id(id).await?
            .ok_or("Form not found")?;
        
        if form.is_locked {
            return Err("Form is already locked".into());
        }
        
        self.form_repository.lock_form(id, locked_by).await
    }

    pub async fn unlock_form(&self, id: Uuid, user_id: Uuid) -> Result<(), Box<dyn std::error::Error>> {
        let form = self.form_repository.find_by_id(id).await?
            .ok_or("Form not found")?;
        
        if !form.is_locked {
            return Err("Form is not locked".into());
        }
        
        // Only the user who locked it or an admin can unlock
        if form.locked_by != Some(user_id) {
            return Err("You don't have permission to unlock this form".into());
        }
        
        self.form_repository.unlock_form(id).await
    }

    pub async fn change_status(&self, id: Uuid, new_status: String, modified_by: Option<Uuid>) -> Result<Form, Box<dyn std::error::Error>> {
        let mut form = self.form_repository.find_by_id(id).await?
            .ok_or("Form not found")?;
        
        // Validate status transition
        self.validate_status_transition(&form.status, &new_status)?;
        
        form.status = new_status;
        form.modified_date = chrono::Utc::now();
        form.modified_user_id = modified_by;
        
        self.form_repository.update(form).await
    }

    fn validate_status_transition(&self, current_status: &str, new_status: &str) -> Result<(), Box<dyn std::error::Error>> {
        match (current_status, new_status) {
            ("draft", "pending") => Ok(()),
            ("draft", "cancelled") => Ok(()),
            ("pending", "approved") => Ok(()),
            ("pending", "rejected") => Ok(()),
            ("pending", "cancelled") => Ok(()),
            ("approved", "completed") => Ok(()),
            ("approved", "cancelled") => Ok(()),
            _ => Err(format!("Invalid status transition from {} to {}", current_status, new_status).into()),
        }
    }
}
