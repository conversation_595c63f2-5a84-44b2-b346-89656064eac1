use uuid::Uuid;

use crate::domain::entities::{<PERSON>ant, CreateTenantRequest, UpdateTenantRequest};
use crate::domain::repositories::TenantRepository;
use crate::domain::value_objects::{PaginationRequest, Pagination, SearchQuery};

pub struct TenantService {
    tenant_repository: Box<dyn TenantRepository>,
}

impl TenantService {
    pub fn new(tenant_repository: Box<dyn TenantRepository>) -> Self {
        Self { tenant_repository }
    }

    pub async fn create_tenant(&self, request: CreateTenantRequest, created_by: Option<Uuid>) -> Result<Tenant, Box<dyn std::error::Error>> {
        // Validate slug uniqueness
        if let Some(_) = self.tenant_repository.find_by_slug(&request.slug).await? {
            return Err("Slug already exists".into());
        }
        
        // Validate domain uniqueness if provided
        if let Some(domain) = &request.domain {
            if let Some(_) = self.tenant_repository.find_by_domain(domain).await? {
                return Err("Domain already exists".into());
            }
        }
        
        let tenant = Tenant::new(request, created_by);
        self.tenant_repository.create(tenant).await
    }

    pub async fn get_tenant(&self, id: Uuid) -> Result<Option<Tenant>, Box<dyn std::error::Error>> {
        self.tenant_repository.find_by_id(id).await
    }

    pub async fn get_tenant_by_slug(&self, slug: &str) -> Result<Option<Tenant>, Box<dyn std::error::Error>> {
        self.tenant_repository.find_by_slug(slug).await
    }

    pub async fn get_tenant_by_domain(&self, domain: &str) -> Result<Option<Tenant>, Box<dyn std::error::Error>> {
        self.tenant_repository.find_by_domain(domain).await
    }

    pub async fn update_tenant(&self, id: Uuid, request: UpdateTenantRequest, modified_by: Option<Uuid>) -> Result<Tenant, Box<dyn std::error::Error>> {
        let mut tenant = self.tenant_repository.find_by_id(id).await?
            .ok_or("Tenant not found")?;
        
        tenant.update(request, modified_by);
        self.tenant_repository.update(tenant).await
    }

    pub async fn delete_tenant(&self, id: Uuid) -> Result<(), Box<dyn std::error::Error>> {
        self.tenant_repository.delete(id).await
    }

    pub async fn list_tenants(&self, pagination: PaginationRequest, search: SearchQuery) -> Result<(Vec<Tenant>, Pagination), Box<dyn std::error::Error>> {
        self.tenant_repository.list(pagination, search).await
    }

    pub async fn activate_tenant(&self, id: Uuid) -> Result<(), Box<dyn std::error::Error>> {
        self.tenant_repository.activate(id).await
    }

    pub async fn deactivate_tenant(&self, id: Uuid) -> Result<(), Box<dyn std::error::Error>> {
        self.tenant_repository.deactivate(id).await
    }
}
