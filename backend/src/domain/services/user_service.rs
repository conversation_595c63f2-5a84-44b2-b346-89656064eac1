use async_trait::async_trait;
use uuid::Uuid;

use crate::domain::entities::{User, CreateUserRequest, UpdateUserRequest};
use crate::domain::repositories::UserRepository;
use crate::domain::value_objects::{PaginationRequest, Pagination, <PERSON>Query, Email, PlainPassword};

pub struct UserService {
    user_repository: Box<dyn UserRepository>,
}

impl UserService {
    pub fn new(user_repository: Box<dyn UserRepository>) -> Self {
        Self { user_repository }
    }

    pub async fn create_user(&self, request: CreateUserRequest) -> Result<User, Box<dyn std::error::Error>> {
        // Validate email
        let _email = Email::new(request.email.clone())?;
        
        // Validate password
        let _password = PlainPassword::new(request.password.clone())?;
        
        // Check if email already exists
        if let Some(_) = self.user_repository.find_by_email(&request.email).await? {
            return Err("<PERSON><PERSON> already exists".into());
        }
        
        // Check if username already exists
        if let Some(_) = self.user_repository.find_by_username(&request.username).await? {
            return Err("Username already exists".into());
        }
        
        // Hash password (this will be implemented in infrastructure layer)
        let password_hash = format!("hashed_{}", request.password); // Placeholder
        
        let user = User::new(request, password_hash);
        self.user_repository.create(user).await
    }

    pub async fn get_user(&self, id: Uuid) -> Result<Option<User>, Box<dyn std::error::Error>> {
        self.user_repository.find_by_id(id).await
    }

    pub async fn update_user(&self, id: Uuid, request: UpdateUserRequest, modified_by: Option<Uuid>) -> Result<User, Box<dyn std::error::Error>> {
        let mut user = self.user_repository.find_by_id(id).await?
            .ok_or("User not found")?;
        
        user.update(request, modified_by);
        self.user_repository.update(user).await
    }

    pub async fn delete_user(&self, id: Uuid) -> Result<(), Box<dyn std::error::Error>> {
        self.user_repository.delete(id).await
    }

    pub async fn list_users(&self, pagination: PaginationRequest, search: SearchQuery) -> Result<(Vec<User>, Pagination), Box<dyn std::error::Error>> {
        self.user_repository.list(pagination, search).await
    }

    pub async fn activate_user(&self, id: Uuid) -> Result<(), Box<dyn std::error::Error>> {
        self.user_repository.activate(id).await
    }

    pub async fn deactivate_user(&self, id: Uuid) -> Result<(), Box<dyn std::error::Error>> {
        self.user_repository.deactivate(id).await
    }

    pub async fn find_by_email(&self, email: &str) -> Result<Option<User>, Box<dyn std::error::Error>> {
        self.user_repository.find_by_email(email).await
    }

    pub async fn find_by_username(&self, username: &str) -> Result<Option<User>, Box<dyn std::error::Error>> {
        self.user_repository.find_by_username(username).await
    }
}
