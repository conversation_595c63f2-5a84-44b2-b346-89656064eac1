use serde::{Deserialize, Serialize};
use std::env;

#[derive(Debug, <PERSON><PERSON>, Serial<PERSON>, Deserialize)]
pub struct AppConfig {
    pub server: ServerConfig,
    pub database: DatabaseConfig,
    pub redis: RedisConfig,
    pub jwt: JwtConfig,
    pub file_storage: FileStorageConfig,
    pub security: SecurityConfig,
    pub rate_limiting: RateLimitingConfig,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ServerConfig {
    pub host: String,
    pub port: u16,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct DatabaseConfig {
    pub url: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RedisConfig {
    pub url: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JwtConfig {
    pub secret: String,
    pub expiration: u64,
    pub refresh_expiration: u64,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct FileStorageConfig {
    pub path: String,
    pub max_file_size: u64,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SecurityConfig {
    pub cors_allowed_origins: Vec<String>,
    pub cors_allowed_methods: Vec<String>,
    pub cors_allowed_headers: Vec<String>,
    pub cors_max_age: u64,
    pub enable_hsts: bool,
    pub hsts_max_age: u64,
    pub enable_csp: bool,
    pub csp_policy: String,
    pub max_request_size: u64,
    pub enable_request_validation: bool,
    pub blocked_user_agents: Vec<String>,
    pub blocked_ips: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimitingConfig {
    pub enabled: bool,
    pub general_max_requests: u32,
    pub general_window_seconds: u64,
    pub auth_max_requests: u32,
    pub auth_window_seconds: u64,
    pub upload_max_requests: u32,
    pub upload_window_seconds: u64,
    pub export_max_requests: u32,
    pub export_window_seconds: u64,
    pub per_ip: bool,
    pub per_user: bool,
}



impl AppConfig {
    pub async fn from_env() -> Result<Self, Box<dyn std::error::Error>> {
        dotenvy::dotenv().ok();

        Ok(Self {
            server: ServerConfig {
                host: env::var("SERVER_HOST").unwrap_or_else(|_| "0.0.0.0".to_string()),
                port: env::var("SERVER_PORT")
                    .unwrap_or_else(|_| "8000".to_string())
                    .parse()
                    .unwrap_or(8000),
            },
            database: DatabaseConfig {
                url: env::var("DATABASE_URL")
                    .expect("DATABASE_URL must be set"),
            },
            redis: RedisConfig {
                url: env::var("REDIS_URL")
                    .expect("REDIS_URL must be set"),
            },
            jwt: JwtConfig {
                secret: env::var("JWT_SECRET")
                    .expect("JWT_SECRET must be set"),
                expiration: env::var("JWT_EXPIRATION")
                    .unwrap_or_else(|_| "3600".to_string())
                    .parse()
                    .unwrap_or(3600),
                refresh_expiration: env::var("JWT_REFRESH_EXPIRATION")
                    .unwrap_or_else(|_| "604800".to_string())
                    .parse()
                    .unwrap_or(604800),
            },
            file_storage: FileStorageConfig {
                path: env::var("FILE_STORAGE_PATH")
                    .unwrap_or_else(|_| "./storage".to_string()),
                max_file_size: env::var("MAX_FILE_SIZE")
                    .unwrap_or_else(|_| "104857600".to_string())
                    .parse()
                    .unwrap_or(104857600),
            },
            security: SecurityConfig {
                cors_allowed_origins: env::var("CORS_ALLOWED_ORIGINS")
                    .unwrap_or_else(|_| "http://localhost:3000,https://localhost:3000".to_string())
                    .split(',')
                    .map(|s| s.trim().to_string())
                    .collect(),
                cors_allowed_methods: env::var("CORS_ALLOWED_METHODS")
                    .unwrap_or_else(|_| "GET,POST,PUT,DELETE,OPTIONS".to_string())
                    .split(',')
                    .map(|s| s.trim().to_string())
                    .collect(),
                cors_allowed_headers: env::var("CORS_ALLOWED_HEADERS")
                    .unwrap_or_else(|_| "Content-Type,Authorization,X-Tenant-ID,X-Request-ID".to_string())
                    .split(',')
                    .map(|s| s.trim().to_string())
                    .collect(),
                cors_max_age: env::var("CORS_MAX_AGE")
                    .unwrap_or_else(|_| "86400".to_string())
                    .parse()
                    .unwrap_or(86400),
                enable_hsts: env::var("ENABLE_HSTS")
                    .unwrap_or_else(|_| "true".to_string())
                    .parse()
                    .unwrap_or(true),
                hsts_max_age: env::var("HSTS_MAX_AGE")
                    .unwrap_or_else(|_| "31536000".to_string())
                    .parse()
                    .unwrap_or(31536000),
                enable_csp: env::var("ENABLE_CSP")
                    .unwrap_or_else(|_| "true".to_string())
                    .parse()
                    .unwrap_or(true),
                csp_policy: env::var("CSP_POLICY")
                    .unwrap_or_else(|_| "default-src 'none'; script-src 'none'; style-src 'none'; img-src 'none'; font-src 'none'; connect-src 'self'; frame-ancestors 'none'; base-uri 'none'; form-action 'none'".to_string()),
                max_request_size: env::var("MAX_REQUEST_SIZE")
                    .unwrap_or_else(|_| "104857600".to_string())
                    .parse()
                    .unwrap_or(104857600),
                enable_request_validation: env::var("ENABLE_REQUEST_VALIDATION")
                    .unwrap_or_else(|_| "true".to_string())
                    .parse()
                    .unwrap_or(true),
                blocked_user_agents: env::var("BLOCKED_USER_AGENTS")
                    .unwrap_or_else(|_| "".to_string())
                    .split(',')
                    .filter(|s| !s.trim().is_empty())
                    .map(|s| s.trim().to_string())
                    .collect(),
                blocked_ips: env::var("BLOCKED_IPS")
                    .unwrap_or_else(|_| "".to_string())
                    .split(',')
                    .filter(|s| !s.trim().is_empty())
                    .map(|s| s.trim().to_string())
                    .collect(),
            },
            rate_limiting: RateLimitingConfig {
                enabled: env::var("RATE_LIMITING_ENABLED")
                    .unwrap_or_else(|_| "true".to_string())
                    .parse()
                    .unwrap_or(true),
                general_max_requests: env::var("RATE_LIMIT_GENERAL_MAX")
                    .unwrap_or_else(|_| "100".to_string())
                    .parse()
                    .unwrap_or(100),
                general_window_seconds: env::var("RATE_LIMIT_GENERAL_WINDOW")
                    .unwrap_or_else(|_| "60".to_string())
                    .parse()
                    .unwrap_or(60),
                auth_max_requests: env::var("RATE_LIMIT_AUTH_MAX")
                    .unwrap_or_else(|_| "10".to_string())
                    .parse()
                    .unwrap_or(10),
                auth_window_seconds: env::var("RATE_LIMIT_AUTH_WINDOW")
                    .unwrap_or_else(|_| "60".to_string())
                    .parse()
                    .unwrap_or(60),
                upload_max_requests: env::var("RATE_LIMIT_UPLOAD_MAX")
                    .unwrap_or_else(|_| "5".to_string())
                    .parse()
                    .unwrap_or(5),
                upload_window_seconds: env::var("RATE_LIMIT_UPLOAD_WINDOW")
                    .unwrap_or_else(|_| "60".to_string())
                    .parse()
                    .unwrap_or(60),
                export_max_requests: env::var("RATE_LIMIT_EXPORT_MAX")
                    .unwrap_or_else(|_| "20".to_string())
                    .parse()
                    .unwrap_or(20),
                export_window_seconds: env::var("RATE_LIMIT_EXPORT_WINDOW")
                    .unwrap_or_else(|_| "300".to_string())
                    .parse()
                    .unwrap_or(300),
                per_ip: env::var("RATE_LIMIT_PER_IP")
                    .unwrap_or_else(|_| "true".to_string())
                    .parse()
                    .unwrap_or(true),
                per_user: env::var("RATE_LIMIT_PER_USER")
                    .unwrap_or_else(|_| "false".to_string())
                    .parse()
                    .unwrap_or(false),
            },
        })
    }
}