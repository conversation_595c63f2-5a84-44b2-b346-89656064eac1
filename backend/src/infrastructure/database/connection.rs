use sqlx::{PgPool, Pool, Postgres};
use std::time::Duration;
use tracing::{info, error, warn};

use crate::infrastructure::config::DatabaseConfig;

/// Create an optimized PostgreSQL connection pool
pub async fn create_connection_pool(config: &DatabaseConfig) -> Result<PgPool, sqlx::Error> {
    info!("Creating optimized database connection pool");

    let pool = sqlx::postgres::PgPoolOptions::new()
        // Connection pool sizing based on expected load (fallback to 20 if can't detect CPUs)
        .max_connections(20)
        .min_connections(5)
        // Timeouts optimized for production
        .acquire_timeout(Duration::from_secs(30))
        .idle_timeout(Duration::from_secs(600)) // 10 minutes
        .max_lifetime(Duration::from_secs(1800)) // 30 minutes
        // Connection testing
        .test_before_acquire(true)
        // Custom connection setup for performance
        .after_connect(|conn, _meta| {
            Box::pin(async move {
                // Set connection-level optimizations in a single query for efficiency
                sqlx::query(
                    r#"
                    SET statement_timeout = '30s';
                    SET lock_timeout = '10s';
                    SET idle_in_transaction_session_timeout = '60s';
                    SET plan_cache_mode = 'auto';
                    SET random_page_cost = 1.1;
                    "#
                )
                .execute(conn)
                .await?;

                Ok(())
            })
        })
        .connect(&config.url)
        .await?;

    info!("Optimized database connection pool created successfully with {} max connections",
          pool.options().get_max_connections());

    // Enable pg_stat_statements for query monitoring
    enable_query_monitoring(&pool).await?;

    Ok(pool)
}

/// Enable PostgreSQL query monitoring extensions
async fn enable_query_monitoring(pool: &PgPool) -> Result<(), sqlx::Error> {
    // Try to enable pg_stat_statements if not already enabled
    let result = sqlx::query("CREATE EXTENSION IF NOT EXISTS pg_stat_statements")
        .execute(pool)
        .await;

    match result {
        Ok(_) => {
            info!("pg_stat_statements extension enabled for query monitoring");
        }
        Err(e) => {
            warn!("Could not enable pg_stat_statements extension: {}. Query monitoring will be limited.", e);
        }
    }

    Ok(())
}

/// Test database connection
pub async fn test_connection(pool: &Pool<Postgres>) -> Result<(), sqlx::Error> {
    info!("Testing database connection");
    
    let row: (i64,) = sqlx::query_as("SELECT 1")
        .fetch_one(pool)
        .await?;
    
    if row.0 == 1 {
        info!("Database connection test successful");
        Ok(())
    } else {
        error!("Database connection test failed");
        Err(sqlx::Error::Protocol("Connection test failed".to_string()))
    }
}

/// Run database migrations
pub async fn run_migrations(pool: &Pool<Postgres>) -> Result<(), sqlx::migrate::MigrateError> {
    info!("Running database migrations");
    
    sqlx::migrate!("./migrations")
        .run(pool)
        .await?;
    
    info!("Database migrations completed successfully");
    Ok(())
}
