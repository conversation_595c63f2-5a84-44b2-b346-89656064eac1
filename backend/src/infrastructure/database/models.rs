use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use serde_json::Value as JsonValue;
use sqlx::FromRow;
use uuid::Uuid;

/// Database model for audit_logs table
#[derive(Debug, <PERSON>lone, FromRow, Serialize, Deserialize)]
pub struct AuditLogModel {
    pub id: Uuid,
    pub tenant_id: Option<Uuid>,
    pub user_id: Option<Uuid>,
    pub impersonated_by: Option<Uuid>,
    pub action: String,
    pub entity_type: String,
    pub entity_id: Uuid,
    pub old_values: Option<JsonValue>,
    pub new_values: Option<JsonValue>,
    pub changes: Option<JsonValue>,
    pub request_id: Option<String>,
    pub session_id: Option<String>,
    pub ip_address: Option<String>,
    pub user_agent: Option<String>,
    pub route: Option<String>,
    pub method: Option<String>,
    pub status_code: Option<i32>,
    pub created_date: DateTime<Utc>,
}

/// Database model for system_logs table
#[derive(Debug, <PERSON><PERSON>, FromRow, Serialize, Deserialize)]
pub struct SystemLogModel {
    pub id: Uuid,
    pub level: String,
    pub message: String,
    pub context: Option<JsonValue>,
    pub trace_id: Option<String>,
    pub span_id: Option<String>,
    pub source: Option<String>,
    pub created_date: DateTime<Utc>,
}

/// Database model for user_sessions table
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct UserSessionModel {
    pub id: Uuid,
    pub user_id: Uuid,
    pub tenant_id: Uuid,
    pub session_token: String,
    pub refresh_token: String,
    pub expires_at: DateTime<Utc>,
    pub refresh_expires_at: DateTime<Utc>,
    pub ip_address: Option<String>,
    pub user_agent: Option<String>,
    pub device_info: Option<JsonValue>,
    pub is_active: bool,
    pub last_activity: DateTime<Utc>,
    pub created_date: DateTime<Utc>,
}

/// Database model for file_access_logs table
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct FileAccessLogModel {
    pub id: Uuid,
    pub attachment_id: Uuid,
    pub user_id: Option<Uuid>,
    pub action: String,
    pub ip_address: Option<String>,
    pub user_agent: Option<String>,
    pub created_date: DateTime<Utc>,
}

/// Conversion from domain entity to database model
impl From<crate::domain::entities::AuditLog> for AuditLogModel {
    fn from(audit_log: crate::domain::entities::AuditLog) -> Self {
        Self {
            id: audit_log.id,
            tenant_id: audit_log.tenant_id,
            user_id: audit_log.user_id,
            impersonated_by: audit_log.impersonated_by,
            action: audit_log.action,
            entity_type: audit_log.entity_type,
            entity_id: audit_log.entity_id,
            old_values: audit_log.old_values,
            new_values: audit_log.new_values,
            changes: audit_log.changes,
            request_id: audit_log.request_id,
            session_id: audit_log.session_id,
            ip_address: audit_log.ip_address,
            user_agent: audit_log.user_agent,
            route: audit_log.route,
            method: audit_log.method,
            status_code: audit_log.status_code,
            created_date: audit_log.created_date,
        }
    }
}

/// Conversion from database model to domain entity
impl From<AuditLogModel> for crate::domain::entities::AuditLog {
    fn from(model: AuditLogModel) -> Self {
        Self {
            id: model.id,
            tenant_id: model.tenant_id,
            user_id: model.user_id,
            impersonated_by: model.impersonated_by,
            action: model.action,
            entity_type: model.entity_type,
            entity_id: model.entity_id,
            old_values: model.old_values,
            new_values: model.new_values,
            changes: model.changes,
            request_id: model.request_id,
            session_id: model.session_id,
            ip_address: model.ip_address,
            user_agent: model.user_agent,
            route: model.route,
            method: model.method,
            status_code: model.status_code,
            created_date: model.created_date,
        }
    }
}
