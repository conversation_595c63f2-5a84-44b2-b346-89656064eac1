use std::time::{Duration, Instant};
use sqlx::{PgPool, Row};
use tracing::{info, warn, error};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

/// Database performance metrics
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct DatabaseMetrics {
    pub active_connections: u32,
    pub idle_connections: u32,
    pub total_connections: u32,
    pub slow_queries_count: u64,
    pub average_query_time_ms: f64,
    pub cache_hit_ratio: f64,
    pub deadlocks_count: u64,
    pub table_sizes: Vec<TableSize>,
    pub index_usage: Vec<IndexUsage>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TableSize {
    pub table_name: String,
    pub size_bytes: i64,
    pub row_count: i64,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct IndexUsage {
    pub table_name: String,
    pub index_name: String,
    pub scans: i64,
    pub tuples_read: i64,
    pub tuples_fetched: i64,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SlowQuery {
    pub query: String,
    pub duration_ms: f64,
    pub calls: i64,
    pub mean_time_ms: f64,
    pub rows: i64,
}

/// Database performance monitor
pub struct DatabasePerformanceMonitor {
    pool: PgPool,
    slow_query_threshold_ms: u64,
}

impl DatabasePerformanceMonitor {
    pub fn new(pool: PgPool, slow_query_threshold_ms: u64) -> Self {
        Self {
            pool,
            slow_query_threshold_ms,
        }
    }

    /// Get comprehensive database metrics
    pub async fn get_metrics(&self) -> Result<DatabaseMetrics, sqlx::Error> {
        let start = Instant::now();

        // Get connection pool stats
        let active_connections = self.pool.size();
        let idle_connections = self.pool.num_idle();
        let total_connections = active_connections;

        // Get slow queries count
        let slow_queries_count = self.get_slow_queries_count().await?;

        // Get average query time
        let average_query_time_ms = self.get_average_query_time().await?;

        // Get cache hit ratio
        let cache_hit_ratio = self.get_cache_hit_ratio().await?;

        // Get deadlocks count
        let deadlocks_count = self.get_deadlocks_count().await?;

        // Get table sizes
        let table_sizes = self.get_table_sizes().await?;

        // Get index usage
        let index_usage = self.get_index_usage().await?;

        let metrics = DatabaseMetrics {
            active_connections,
            idle_connections: idle_connections as u32,
            total_connections,
            slow_queries_count,
            average_query_time_ms,
            cache_hit_ratio,
            deadlocks_count,
            table_sizes,
            index_usage,
        };

        let duration = start.elapsed();
        info!("Database metrics collected in {}ms", duration.as_millis());

        Ok(metrics)
    }

    /// Get slow queries from pg_stat_statements
    pub async fn get_slow_queries(&self, limit: i32) -> Result<Vec<SlowQuery>, sqlx::Error> {
        let queries = sqlx::query(
            r#"
            SELECT 
                query,
                total_exec_time as duration_ms,
                calls,
                mean_exec_time as mean_time_ms,
                rows
            FROM pg_stat_statements 
            WHERE mean_exec_time > $1
            ORDER BY mean_exec_time DESC 
            LIMIT $2
            "#,
        )
        .bind(self.slow_query_threshold_ms as f64)
        .bind(limit)
        .fetch_all(&self.pool)
        .await?;

        let slow_queries = queries
            .into_iter()
            .map(|row| SlowQuery {
                query: row.get("query"),
                duration_ms: row.get("duration_ms"),
                calls: row.get("calls"),
                mean_time_ms: row.get("mean_time_ms"),
                rows: row.get("rows"),
            })
            .collect();

        Ok(slow_queries)
    }

    /// Get count of slow queries
    async fn get_slow_queries_count(&self) -> Result<u64, sqlx::Error> {
        let count: i64 = sqlx::query_scalar(
            "SELECT COUNT(*) FROM pg_stat_statements WHERE mean_exec_time > $1"
        )
        .bind(self.slow_query_threshold_ms as f64)
        .fetch_one(&self.pool)
        .await
        .unwrap_or(0);

        Ok(count as u64)
    }

    /// Get average query execution time
    async fn get_average_query_time(&self) -> Result<f64, sqlx::Error> {
        let avg_time: Option<f64> = sqlx::query_scalar(
            "SELECT AVG(mean_exec_time) FROM pg_stat_statements"
        )
        .fetch_one(&self.pool)
        .await
        .unwrap_or(Some(0.0));

        Ok(avg_time.unwrap_or(0.0))
    }

    /// Get cache hit ratio
    async fn get_cache_hit_ratio(&self) -> Result<f64, sqlx::Error> {
        let ratio: Option<f64> = sqlx::query_scalar(
            r#"
            SELECT 
                CASE 
                    WHEN (blks_hit + blks_read) = 0 THEN 0
                    ELSE (blks_hit::float / (blks_hit + blks_read)) * 100
                END as cache_hit_ratio
            FROM pg_stat_database 
            WHERE datname = current_database()
            "#,
        )
        .fetch_one(&self.pool)
        .await
        .unwrap_or(Some(0.0));

        Ok(ratio.unwrap_or(0.0))
    }

    /// Get deadlocks count
    async fn get_deadlocks_count(&self) -> Result<u64, sqlx::Error> {
        let count: Option<i64> = sqlx::query_scalar(
            "SELECT deadlocks FROM pg_stat_database WHERE datname = current_database()"
        )
        .fetch_one(&self.pool)
        .await
        .unwrap_or(Some(0));

        Ok(count.unwrap_or(0) as u64)
    }

    /// Get table sizes
    async fn get_table_sizes(&self) -> Result<Vec<TableSize>, sqlx::Error> {
        let rows = sqlx::query(
            r#"
            SELECT 
                schemaname||'.'||tablename as table_name,
                pg_total_relation_size(schemaname||'.'||tablename) as size_bytes,
                n_tup_ins + n_tup_upd + n_tup_del as row_count
            FROM pg_stat_user_tables 
            ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
            LIMIT 20
            "#,
        )
        .fetch_all(&self.pool)
        .await?;

        let table_sizes = rows
            .into_iter()
            .map(|row| TableSize {
                table_name: row.get("table_name"),
                size_bytes: row.get("size_bytes"),
                row_count: row.get("row_count"),
            })
            .collect();

        Ok(table_sizes)
    }

    /// Get index usage statistics
    async fn get_index_usage(&self) -> Result<Vec<IndexUsage>, sqlx::Error> {
        let rows = sqlx::query(
            r#"
            SELECT 
                schemaname||'.'||tablename as table_name,
                indexname as index_name,
                idx_scan as scans,
                idx_tup_read as tuples_read,
                idx_tup_fetch as tuples_fetched
            FROM pg_stat_user_indexes 
            ORDER BY idx_scan DESC
            LIMIT 50
            "#,
        )
        .fetch_all(&self.pool)
        .await?;

        let index_usage = rows
            .into_iter()
            .map(|row| IndexUsage {
                table_name: row.get("table_name"),
                index_name: row.get("index_name"),
                scans: row.get("scans"),
                tuples_read: row.get("tuples_read"),
                tuples_fetched: row.get("tuples_fetched"),
            })
            .collect();

        Ok(index_usage)
    }

    /// Analyze query performance and suggest optimizations
    pub async fn analyze_query_performance(&self) -> Result<Vec<String>, sqlx::Error> {
        let mut suggestions = Vec::new();

        // Check for unused indexes
        let unused_indexes = self.get_unused_indexes().await?;
        if !unused_indexes.is_empty() {
            suggestions.push(format!(
                "Consider dropping {} unused indexes: {}",
                unused_indexes.len(),
                unused_indexes.join(", ")
            ));
        }

        // Check for missing indexes on foreign keys
        let missing_fk_indexes = self.get_missing_foreign_key_indexes().await?;
        if !missing_fk_indexes.is_empty() {
            suggestions.push(format!(
                "Consider adding indexes on foreign key columns: {}",
                missing_fk_indexes.join(", ")
            ));
        }

        // Check cache hit ratio
        let cache_hit_ratio = self.get_cache_hit_ratio().await?;
        if cache_hit_ratio < 95.0 {
            suggestions.push(format!(
                "Cache hit ratio is {:.1}% (should be >95%). Consider increasing shared_buffers.",
                cache_hit_ratio
            ));
        }

        Ok(suggestions)
    }

    /// Get unused indexes
    async fn get_unused_indexes(&self) -> Result<Vec<String>, sqlx::Error> {
        let rows = sqlx::query(
            r#"
            SELECT schemaname||'.'||indexname as index_name
            FROM pg_stat_user_indexes 
            WHERE idx_scan = 0
            AND schemaname = 'public'
            "#,
        )
        .fetch_all(&self.pool)
        .await?;

        let unused_indexes = rows
            .into_iter()
            .map(|row| row.get::<String, _>("index_name"))
            .collect();

        Ok(unused_indexes)
    }

    /// Get foreign key columns without indexes
    async fn get_missing_foreign_key_indexes(&self) -> Result<Vec<String>, sqlx::Error> {
        let rows = sqlx::query(
            r#"
            SELECT 
                tc.table_name||'.'||kcu.column_name as column_name
            FROM information_schema.table_constraints tc
            JOIN information_schema.key_column_usage kcu 
                ON tc.constraint_name = kcu.constraint_name
            LEFT JOIN pg_stat_user_indexes psi 
                ON psi.schemaname = tc.table_schema 
                AND psi.tablename = tc.table_name
                AND psi.indexname LIKE '%'||kcu.column_name||'%'
            WHERE tc.constraint_type = 'FOREIGN KEY'
            AND tc.table_schema = 'public'
            AND psi.indexname IS NULL
            "#,
        )
        .fetch_all(&self.pool)
        .await?;

        let missing_indexes = rows
            .into_iter()
            .map(|row| row.get::<String, _>("column_name"))
            .collect();

        Ok(missing_indexes)
    }

    /// Record performance metrics to database
    pub async fn record_metrics(&self, tenant_id: Option<Uuid>) -> Result<(), sqlx::Error> {
        let metrics = self.get_metrics().await?;

        // Record connection metrics
        sqlx::query(
            r#"
            INSERT INTO performance_metrics (metric_name, metric_value, metric_unit, tenant_id)
            VALUES 
                ('db_active_connections', $1, 'count', $4),
                ('db_idle_connections', $2, 'count', $4),
                ('db_cache_hit_ratio', $3, 'percent', $4)
            "#,
        )
        .bind(metrics.active_connections as f64)
        .bind(metrics.idle_connections as f64)
        .bind(metrics.cache_hit_ratio)
        .bind(tenant_id)
        .execute(&self.pool)
        .await?;

        Ok(())
    }
}

/// Query execution timer for automatic performance monitoring
pub struct QueryTimer {
    start: Instant,
    query_type: String,
    threshold_ms: u64,
}

impl QueryTimer {
    pub fn new(query_type: impl Into<String>, threshold_ms: u64) -> Self {
        Self {
            start: Instant::now(),
            query_type: query_type.into(),
            threshold_ms,
        }
    }

    pub fn finish(self) -> Duration {
        let duration = self.start.elapsed();
        
        if duration.as_millis() as u64 > self.threshold_ms {
            warn!(
                "Slow query detected: {} took {}ms (threshold: {}ms)",
                self.query_type,
                duration.as_millis(),
                self.threshold_ms
            );
        } else {
            info!(
                "Query completed: {} took {}ms",
                self.query_type,
                duration.as_millis()
            );
        }

        duration
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_query_timer() {
        let timer = QueryTimer::new("test_query", 100);
        std::thread::sleep(Duration::from_millis(50));
        let duration = timer.finish();
        assert!(duration.as_millis() >= 50);
    }
}
