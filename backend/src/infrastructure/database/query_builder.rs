use sqlx::{Query<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Row};
use std::collections::HashMap;
use uuid::Uuid;

/// Optimized query builder for common patterns
pub struct OptimizedQueryBuilder {
    base_query: String,
    conditions: Vec<String>,
    joins: Vec<String>,
    order_by: Vec<String>,
    limit: Option<i64>,
    offset: Option<i64>,
    parameters: Vec<sqlx::types::Json<serde_json::Value>>,
}

impl OptimizedQueryBuilder {
    pub fn new(base_query: impl Into<String>) -> Self {
        Self {
            base_query: base_query.into(),
            conditions: Vec::new(),
            joins: Vec::new(),
            order_by: Vec::new(),
            limit: None,
            offset: None,
            parameters: Vec::new(),
        }
    }

    /// Add a WHERE condition with parameter binding
    pub fn where_condition(mut self, condition: impl Into<String>) -> Self {
        self.conditions.push(condition.into());
        self
    }

    /// Add a conditional WHERE clause (only if condition is Some)
    pub fn where_optional<T>(mut self, field: &str, value: Option<T>) -> Self 
    where
        T: ToString,
    {
        if let Some(val) = value {
            self.conditions.push(format!("{} = '{}'", field, val.to_string()));
        }
        self
    }

    /// Add a WHERE IN clause for multiple values
    pub fn where_in<T>(mut self, field: &str, values: Vec<T>) -> Self 
    where
        T: ToString,
    {
        if !values.is_empty() {
            let value_list = values
                .into_iter()
                .map(|v| format!("'{}'", v.to_string()))
                .collect::<Vec<_>>()
                .join(", ");
            self.conditions.push(format!("{} IN ({})", field, value_list));
        }
        self
    }

    /// Add a date range filter
    pub fn date_range(mut self, field: &str, start: Option<chrono::DateTime<chrono::Utc>>, end: Option<chrono::DateTime<chrono::Utc>>) -> Self {
        if let Some(start_date) = start {
            self.conditions.push(format!("{} >= '{}'", field, start_date.to_rfc3339()));
        }
        if let Some(end_date) = end {
            self.conditions.push(format!("{} <= '{}'", field, end_date.to_rfc3339()));
        }
        self
    }

    /// Add a text search condition using ILIKE
    pub fn text_search(mut self, fields: Vec<&str>, search_term: &str) -> Self {
        if !search_term.is_empty() {
            let search_pattern = format!("%{}%", search_term);
            let conditions = fields
                .into_iter()
                .map(|field| format!("{} ILIKE '{}'", field, search_pattern))
                .collect::<Vec<_>>()
                .join(" OR ");
            
            if !conditions.is_empty() {
                self.conditions.push(format!("({})", conditions));
            }
        }
        self
    }

    /// Add a JOIN clause
    pub fn join(mut self, join_clause: impl Into<String>) -> Self {
        self.joins.push(join_clause.into());
        self
    }

    /// Add a LEFT JOIN clause
    pub fn left_join(mut self, table: &str, on_condition: &str) -> Self {
        self.joins.push(format!("LEFT JOIN {} ON {}", table, on_condition));
        self
    }

    /// Add an INNER JOIN clause
    pub fn inner_join(mut self, table: &str, on_condition: &str) -> Self {
        self.joins.push(format!("INNER JOIN {} ON {}", table, on_condition));
        self
    }

    /// Add ORDER BY clause
    pub fn order_by(mut self, field: &str, direction: OrderDirection) -> Self {
        self.order_by.push(format!("{} {}", field, direction.as_str()));
        self
    }

    /// Add pagination
    pub fn paginate(mut self, page: u64, limit: u64) -> Self {
        self.limit = Some(limit as i64);
        self.offset = Some(((page - 1) * limit) as i64);
        self
    }

    /// Build the final query string
    pub fn build(self) -> String {
        let mut query = self.base_query;

        // Add JOINs
        if !self.joins.is_empty() {
            query.push(' ');
            query.push_str(&self.joins.join(" "));
        }

        // Add WHERE conditions
        if !self.conditions.is_empty() {
            query.push_str(" WHERE ");
            query.push_str(&self.conditions.join(" AND "));
        }

        // Add ORDER BY
        if !self.order_by.is_empty() {
            query.push_str(" ORDER BY ");
            query.push_str(&self.order_by.join(", "));
        }

        // Add LIMIT and OFFSET
        if let Some(limit) = self.limit {
            query.push_str(&format!(" LIMIT {}", limit));
        }
        if let Some(offset) = self.offset {
            query.push_str(&format!(" OFFSET {}", offset));
        }

        query
    }

    /// Build a count query for pagination
    pub fn build_count_query(&self) -> String {
        let mut query = format!("SELECT COUNT(*) FROM ({})", self.base_query);

        // Add JOINs
        if !self.joins.is_empty() {
            query = format!("SELECT COUNT(*) FROM ({} {})", self.base_query, self.joins.join(" "));
        }

        // Add WHERE conditions
        if !self.conditions.is_empty() {
            query.push_str(" WHERE ");
            query.push_str(&self.conditions.join(" AND "));
        }

        query.push_str(") as count_query");
        query
    }
}

#[derive(Debug, Clone)]
pub enum OrderDirection {
    Asc,
    Desc,
}

impl OrderDirection {
    fn as_str(&self) -> &'static str {
        match self {
            OrderDirection::Asc => "ASC",
            OrderDirection::Desc => "DESC",
        }
    }
}

/// Optimized query patterns for common operations
pub struct QueryPatterns;

impl QueryPatterns {
    /// Build an optimized audit log query with proper indexing hints
    pub fn audit_logs_query() -> OptimizedQueryBuilder {
        OptimizedQueryBuilder::new(
            r#"
            SELECT 
                id, tenant_id, user_id, impersonated_by, action, entity_type, entity_id,
                old_values, new_values, changes, request_id, session_id, ip_address,
                user_agent, route, method, status_code, created_date
            FROM audit_logs
            "#
        )
    }

    /// Build an optimized user query with role information
    pub fn users_with_roles_query() -> OptimizedQueryBuilder {
        OptimizedQueryBuilder::new(
            r#"
            SELECT 
                u.id, u.tenant_id, u.email, u.first_name, u.last_name, u.is_active,
                u.created_date, u.modified_date,
                COALESCE(
                    json_agg(
                        json_build_object(
                            'id', r.id,
                            'name', r.name,
                            'permissions', r.permissions
                        )
                    ) FILTER (WHERE r.id IS NOT NULL), 
                    '[]'::json
                ) as roles
            FROM users u
            "#
        )
        .left_join("user_roles ur", "u.id = ur.user_id")
        .left_join("roles r", "ur.role_id = r.id")
    }

    /// Build an optimized forms query with related data
    pub fn forms_with_details_query() -> OptimizedQueryBuilder {
        OptimizedQueryBuilder::new(
            r#"
            SELECT 
                f.id, f.tenant_id, f.form_type, f.status, f.priority,
                f.employee_id, f.company_id, f.contact_id,
                f.form_data, f.workflow_state, f.created_date, f.modified_date,
                e.first_name as employee_first_name,
                e.last_name as employee_last_name,
                c.name as company_name,
                con.first_name as contact_first_name,
                con.last_name as contact_last_name,
                COUNT(a.id) as attachment_count
            FROM forms f
            "#
        )
        .left_join("employees e", "f.employee_id = e.id")
        .left_join("companies c", "f.company_id = c.id")
        .left_join("contacts con", "f.contact_id = con.id")
        .left_join("attachments a", "f.id = a.form_id")
    }

    /// Build a query for dashboard statistics
    pub fn dashboard_stats_query(tenant_id: Uuid) -> String {
        format!(
            r#"
            WITH form_stats AS (
                SELECT 
                    COUNT(*) as total_forms,
                    COUNT(*) FILTER (WHERE status = 'pending') as pending_forms,
                    COUNT(*) FILTER (WHERE status = 'approved') as approved_forms,
                    COUNT(*) FILTER (WHERE status = 'rejected') as rejected_forms,
                    COUNT(*) FILTER (WHERE created_date >= CURRENT_DATE - INTERVAL '30 days') as recent_forms
                FROM forms 
                WHERE tenant_id = '{}'
            ),
            user_stats AS (
                SELECT 
                    COUNT(*) as total_users,
                    COUNT(*) FILTER (WHERE is_active = true) as active_users,
                    COUNT(*) FILTER (WHERE last_login >= CURRENT_DATE - INTERVAL '7 days') as recent_users
                FROM users 
                WHERE tenant_id = '{}'
            ),
            company_stats AS (
                SELECT 
                    COUNT(*) as total_companies,
                    COUNT(*) FILTER (WHERE is_active = true) as active_companies
                FROM companies 
                WHERE tenant_id = '{}'
            )
            SELECT 
                fs.*,
                us.*,
                cs.*
            FROM form_stats fs
            CROSS JOIN user_stats us
            CROSS JOIN company_stats cs
            "#,
            tenant_id, tenant_id, tenant_id
        )
    }
}

/// Index optimization suggestions
pub struct IndexOptimizer;

impl IndexOptimizer {
    /// Generate index creation statements for common query patterns
    pub fn get_recommended_indexes() -> Vec<String> {
        vec![
            // Audit logs indexes
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_tenant_created ON audit_logs(tenant_id, created_date DESC)".to_string(),
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_user_action ON audit_logs(user_id, action)".to_string(),
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_entity_composite ON audit_logs(entity_type, entity_id, created_date DESC)".to_string(),
            
            // Users indexes
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_tenant_active ON users(tenant_id, is_active)".to_string(),
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email_tenant ON users(email, tenant_id)".to_string(),
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_last_login ON users(last_login DESC)".to_string(),
            
            // Forms indexes
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_forms_tenant_status ON forms(tenant_id, status)".to_string(),
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_forms_tenant_created ON forms(tenant_id, created_date DESC)".to_string(),
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_forms_employee_status ON forms(employee_id, status)".to_string(),
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_forms_company_status ON forms(company_id, status)".to_string(),
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_forms_workflow_state ON forms(workflow_state)".to_string(),
            
            // Companies indexes
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_companies_tenant_active ON companies(tenant_id, is_active)".to_string(),
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_companies_name_tenant ON companies(name, tenant_id)".to_string(),
            
            // Employees indexes
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_employees_tenant_active ON employees(tenant_id, is_active)".to_string(),
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_employees_company_active ON employees(company_id, is_active)".to_string(),
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_employees_name_tenant ON employees(first_name, last_name, tenant_id)".to_string(),
            
            // Contacts indexes
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_contacts_tenant_active ON contacts(tenant_id, is_active)".to_string(),
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_contacts_company_active ON contacts(company_id, is_active)".to_string(),
            
            // User roles indexes
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_roles_user_id ON user_roles(user_id)".to_string(),
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_roles_role_id ON user_roles(role_id)".to_string(),
            
            // Sessions indexes
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_sessions_user_active ON user_sessions(user_id, is_active)".to_string(),
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions(expires_at)".to_string(),
            
            // Performance metrics indexes
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_performance_metrics_name_recorded ON performance_metrics(metric_name, recorded_at DESC)".to_string(),
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_performance_metrics_tenant_recorded ON performance_metrics(tenant_id, recorded_at DESC)".to_string(),
        ]
    }

    /// Generate partial indexes for specific conditions
    pub fn get_partial_indexes() -> Vec<String> {
        vec![
            // Only index active records
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_active_email ON users(email) WHERE is_active = true".to_string(),
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_companies_active_name ON companies(name) WHERE is_active = true".to_string(),
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_employees_active_company ON employees(company_id) WHERE is_active = true".to_string(),
            
            // Only index pending/in-progress forms
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_forms_pending_created ON forms(created_date DESC) WHERE status IN ('pending', 'in_progress')".to_string(),
            
            // Only index recent audit logs
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_recent ON audit_logs(created_date DESC) WHERE created_date >= CURRENT_DATE - INTERVAL '90 days'".to_string(),
        ]
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_query_builder_basic() {
        let query = OptimizedQueryBuilder::new("SELECT * FROM users")
            .where_condition("is_active = true")
            .order_by("created_date", OrderDirection::Desc)
            .paginate(1, 10)
            .build();

        assert!(query.contains("WHERE is_active = true"));
        assert!(query.contains("ORDER BY created_date DESC"));
        assert!(query.contains("LIMIT 10"));
        assert!(query.contains("OFFSET 0"));
    }

    #[test]
    fn test_query_builder_optional_conditions() {
        let query = OptimizedQueryBuilder::new("SELECT * FROM users")
            .where_optional("tenant_id", Some(Uuid::new_v4()))
            .where_optional("email", None::<String>)
            .build();

        assert!(query.contains("WHERE tenant_id ="));
        assert!(!query.contains("email"));
    }

    #[test]
    fn test_query_builder_text_search() {
        let query = OptimizedQueryBuilder::new("SELECT * FROM users")
            .text_search(vec!["first_name", "last_name", "email"], "john")
            .build();

        assert!(query.contains("first_name ILIKE '%john%'"));
        assert!(query.contains("last_name ILIKE '%john%'"));
        assert!(query.contains("email ILIKE '%john%'"));
        assert!(query.contains("OR"));
    }
}
