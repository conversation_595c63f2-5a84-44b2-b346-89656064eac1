use async_trait::async_trait;
use chrono::{DateTime, Utc};
use sqlx::{PgPool, Row};
use std::collections::HashMap;
use uuid::Uuid;

use crate::domain::entities::AuditLog;
use crate::domain::repositories::{
    AuditRepository, CreateAuditLogRequest, AuditLogFilter, AuditStatistics
};
use crate::domain::value_objects::{PaginationRequest, Pagination, SearchQuery};
use crate::infrastructure::database::models::AuditLogModel;

pub struct PostgresAuditRepository {
    pool: PgPool,
}

impl PostgresAuditRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl AuditRepository for PostgresAuditRepository {
    async fn create(&self, request: CreateAuditLogRequest) -> Result<AuditLog, Box<dyn std::error::Error>> {
        let id = Uuid::new_v4();
        let now = Utc::now();

        let model = sqlx::query_as::<_, AuditLogModel>(
            r#"
            INSERT INTO audit_logs (
                id, tenant_id, user_id, impersonated_by, action, entity_type, entity_id,
                old_values, new_values, changes, request_id, session_id, ip_address,
                user_agent, route, method, status_code, created_date
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18)
            RETURNING *
            "#,
        )
        .bind(id)
        .bind(request.tenant_id)
        .bind(request.user_id)
        .bind(request.impersonated_by)
        .bind(&request.action)
        .bind(&request.entity_type)
        .bind(request.entity_id)
        .bind(&request.old_values)
        .bind(&request.new_values)
        .bind(&request.changes)
        .bind(&request.request_id)
        .bind(&request.session_id)
        .bind(&request.ip_address)
        .bind(&request.user_agent)
        .bind(&request.route)
        .bind(&request.method)
        .bind(request.status_code)
        .bind(now)
        .fetch_one(&self.pool)
        .await?;

        Ok(model.into())
    }

    async fn find_by_id(&self, id: Uuid) -> Result<Option<AuditLog>, Box<dyn std::error::Error>> {
        let model = sqlx::query_as::<_, AuditLogModel>(
            "SELECT * FROM audit_logs WHERE id = $1"
        )
        .bind(id)
        .fetch_optional(&self.pool)
        .await?;

        Ok(model.map(Into::into))
    }

    async fn list(
        &self,
        filter: AuditLogFilter,
        pagination: PaginationRequest,
        search: SearchQuery,
    ) -> Result<(Vec<AuditLog>, Pagination), Box<dyn std::error::Error>> {
        let mut query_builder = sqlx::QueryBuilder::new(
            "SELECT * FROM audit_logs WHERE 1=1"
        );
        let mut count_builder = sqlx::QueryBuilder::new(
            "SELECT COUNT(*) FROM audit_logs WHERE 1=1"
        );

        // Apply filters
        if let Some(tenant_id) = filter.tenant_id {
            query_builder.push(" AND tenant_id = ");
            query_builder.push_bind(tenant_id);
            count_builder.push(" AND tenant_id = ");
            count_builder.push_bind(tenant_id);
        }

        if let Some(user_id) = filter.user_id {
            query_builder.push(" AND user_id = ");
            query_builder.push_bind(user_id);
            count_builder.push(" AND user_id = ");
            count_builder.push_bind(user_id);
        }

        if let Some(entity_type) = &filter.entity_type {
            query_builder.push(" AND entity_type = ");
            query_builder.push_bind(entity_type);
            count_builder.push(" AND entity_type = ");
            count_builder.push_bind(entity_type);
        }

        if let Some(entity_id) = filter.entity_id {
            query_builder.push(" AND entity_id = ");
            query_builder.push_bind(entity_id);
            count_builder.push(" AND entity_id = ");
            count_builder.push_bind(entity_id);
        }

        if let Some(action) = &filter.action {
            query_builder.push(" AND action = ");
            query_builder.push_bind(action);
            count_builder.push(" AND action = ");
            count_builder.push_bind(action);
        }

        if let Some(date_from) = filter.date_from {
            query_builder.push(" AND created_date >= ");
            query_builder.push_bind(date_from);
            count_builder.push(" AND created_date >= ");
            count_builder.push_bind(date_from);
        }

        if let Some(date_to) = filter.date_to {
            query_builder.push(" AND created_date <= ");
            query_builder.push_bind(date_to);
            count_builder.push(" AND created_date <= ");
            count_builder.push_bind(date_to);
        }

        if let Some(impersonated_by) = filter.impersonated_by {
            query_builder.push(" AND impersonated_by = ");
            query_builder.push_bind(impersonated_by);
            count_builder.push(" AND impersonated_by = ");
            count_builder.push_bind(impersonated_by);
        }

        // Apply search
        if let Some(query_text) = &search.query {
            if !query_text.is_empty() {
                let search_pattern = format!("%{}%", query_text);
                query_builder.push(" AND (action ILIKE ");
                query_builder.push_bind(search_pattern.clone());
                query_builder.push(" OR entity_type ILIKE ");
                query_builder.push_bind(search_pattern.clone());
                query_builder.push(" OR route ILIKE ");
                query_builder.push_bind(search_pattern.clone());
                query_builder.push(")");

                count_builder.push(" AND (action ILIKE ");
                count_builder.push_bind(search_pattern.clone());
                count_builder.push(" OR entity_type ILIKE ");
                count_builder.push_bind(search_pattern.clone());
                count_builder.push(" OR route ILIKE ");
                count_builder.push_bind(search_pattern);
                count_builder.push(")");
            }
        }

        // Get total count
        let total_count: i64 = count_builder
            .build_query_scalar()
            .fetch_one(&self.pool)
            .await?;

        // Apply ordering and pagination
        query_builder.push(" ORDER BY created_date DESC");
        query_builder.push(" LIMIT ");
        query_builder.push_bind(pagination.per_page() as i64);
        query_builder.push(" OFFSET ");
        query_builder.push_bind(((pagination.page() - 1) * pagination.per_page()) as i64);

        let models: Vec<AuditLogModel> = query_builder
            .build_query_as()
            .fetch_all(&self.pool)
            .await?;

        let audit_logs: Vec<AuditLog> = models.into_iter().map(Into::into).collect();

        let pagination_result = Pagination::new(
            pagination.page(),
            pagination.per_page(),
            total_count as u64,
        );

        Ok((audit_logs, pagination_result))
    }

    async fn find_by_entity(
        &self,
        entity_type: &str,
        entity_id: Uuid,
        pagination: PaginationRequest,
    ) -> Result<(Vec<AuditLog>, Pagination), Box<dyn std::error::Error>> {
        let filter = AuditLogFilter {
            tenant_id: None,
            user_id: None,
            entity_type: Some(entity_type.to_string()),
            entity_id: Some(entity_id),
            action: None,
            date_from: None,
            date_to: None,
            impersonated_by: None,
        };

        self.list(filter, pagination, SearchQuery::default()).await
    }

    async fn find_by_user(
        &self,
        user_id: Uuid,
        pagination: PaginationRequest,
    ) -> Result<(Vec<AuditLog>, Pagination), Box<dyn std::error::Error>> {
        let filter = AuditLogFilter {
            tenant_id: None,
            user_id: Some(user_id),
            entity_type: None,
            entity_id: None,
            action: None,
            date_from: None,
            date_to: None,
            impersonated_by: None,
        };

        self.list(filter, pagination, SearchQuery::default()).await
    }

    async fn find_by_tenant(
        &self,
        tenant_id: Uuid,
        pagination: PaginationRequest,
    ) -> Result<(Vec<AuditLog>, Pagination), Box<dyn std::error::Error>> {
        let filter = AuditLogFilter {
            tenant_id: Some(tenant_id),
            user_id: None,
            entity_type: None,
            entity_id: None,
            action: None,
            date_from: None,
            date_to: None,
            impersonated_by: None,
        };

        self.list(filter, pagination, SearchQuery::default()).await
    }

    async fn delete_older_than(&self, date: DateTime<Utc>) -> Result<u64, Box<dyn std::error::Error>> {
        let result = sqlx::query("DELETE FROM audit_logs WHERE created_date < $1")
            .bind(date)
            .execute(&self.pool)
            .await?;

        Ok(result.rows_affected())
    }

    async fn get_statistics(
        &self,
        tenant_id: Option<Uuid>,
        date_from: DateTime<Utc>,
        date_to: DateTime<Utc>,
    ) -> Result<AuditStatistics, Box<dyn std::error::Error>> {
        let mut base_query = "FROM audit_logs WHERE created_date >= $1 AND created_date <= $2".to_string();
        let mut bind_index = 3;

        if tenant_id.is_some() {
            base_query.push_str(&format!(" AND tenant_id = ${}", bind_index));
            bind_index += 1;
        }

        // Total actions
        let total_query = format!("SELECT COUNT(*) {}", base_query);
        let mut total_query_builder = sqlx::query_scalar::<_, i64>(&total_query)
            .bind(date_from)
            .bind(date_to);
        
        if let Some(tid) = tenant_id {
            total_query_builder = total_query_builder.bind(tid);
        }
        
        let total_actions: i64 = total_query_builder.fetch_one(&self.pool).await?;

        // Unique users
        let users_query = format!("SELECT COUNT(DISTINCT user_id) {}", base_query);
        let mut users_query_builder = sqlx::query_scalar::<_, i64>(&users_query)
            .bind(date_from)
            .bind(date_to);
        
        if let Some(tid) = tenant_id {
            users_query_builder = users_query_builder.bind(tid);
        }
        
        let unique_users: i64 = users_query_builder.fetch_one(&self.pool).await?;

        // Actions by type
        let actions_query = format!("SELECT action, COUNT(*) {}", base_query);
        let actions_query = format!("{} GROUP BY action", actions_query);
        let mut actions_query_builder = sqlx::query(&actions_query)
            .bind(date_from)
            .bind(date_to);
        
        if let Some(tid) = tenant_id {
            actions_query_builder = actions_query_builder.bind(tid);
        }
        
        let action_rows = actions_query_builder.fetch_all(&self.pool).await?;
        let mut actions_by_type = HashMap::new();
        for row in action_rows {
            let action: String = row.get(0);
            let count: i64 = row.get(1);
            actions_by_type.insert(action, count as u64);
        }

        // Actions by entity
        let entities_query = format!("SELECT entity_type, COUNT(*) {}", base_query);
        let entities_query = format!("{} GROUP BY entity_type", entities_query);
        let mut entities_query_builder = sqlx::query(&entities_query)
            .bind(date_from)
            .bind(date_to);
        
        if let Some(tid) = tenant_id {
            entities_query_builder = entities_query_builder.bind(tid);
        }
        
        let entity_rows = entities_query_builder.fetch_all(&self.pool).await?;
        let mut actions_by_entity = HashMap::new();
        for row in entity_rows {
            let entity_type: String = row.get(0);
            let count: i64 = row.get(1);
            actions_by_entity.insert(entity_type, count as u64);
        }

        // Failed actions (status_code >= 400)
        let failed_query = format!("SELECT COUNT(*) {} AND status_code >= 400", base_query);
        let mut failed_query_builder = sqlx::query_scalar::<_, i64>(&failed_query)
            .bind(date_from)
            .bind(date_to);
        
        if let Some(tid) = tenant_id {
            failed_query_builder = failed_query_builder.bind(tid);
        }
        
        let failed_actions: i64 = failed_query_builder.fetch_one(&self.pool).await?;

        // Impersonated actions
        let impersonated_query = format!("SELECT COUNT(*) {} AND impersonated_by IS NOT NULL", base_query);
        let mut impersonated_query_builder = sqlx::query_scalar::<_, i64>(&impersonated_query)
            .bind(date_from)
            .bind(date_to);
        
        if let Some(tid) = tenant_id {
            impersonated_query_builder = impersonated_query_builder.bind(tid);
        }
        
        let impersonated_actions: i64 = impersonated_query_builder.fetch_one(&self.pool).await?;

        Ok(AuditStatistics {
            total_actions: total_actions as u64,
            unique_users: unique_users as u64,
            actions_by_type,
            actions_by_entity,
            failed_actions: failed_actions as u64,
            impersonated_actions: impersonated_actions as u64,
        })
    }
}
