use tracing::{info, warn, error};
use tracing_subscriber::{
    fmt::{self, format::FmtSpan},
    layer::SubscriberExt,
    util::SubscriberInitExt,
    EnvFilter, Registry,
};

/// Initialize structured logging for the application
pub fn init_logging() -> Result<(), Box<dyn std::error::Error>> {
    let env_filter = EnvFilter::try_from_default_env()
        .unwrap_or_else(|_| EnvFilter::new("info"));

    let formatting_layer = fmt::layer()
        .with_target(true)
        .with_thread_ids(true)
        .with_thread_names(true)
        .with_file(true)
        .with_line_number(true)
        .with_span_events(FmtSpan::CLOSE); // Use default format for structured logging

    Registry::default()
        .with(env_filter)
        .with(formatting_layer)
        .init();

    info!("Structured logging initialized");
    Ok(())
}

/// Log application startup information
pub fn log_startup_info() {
    let build_time = std::env::var("BUILD_TIME").unwrap_or_else(|_| "unknown".to_string());
    let git_commit = std::env::var("GIT_COMMIT").unwrap_or_else(|_| "unknown".to_string());

    info!(
        service = "forms-backend",
        version = env!("CARGO_PKG_VERSION"),
        build_time = %build_time,
        git_commit = %git_commit,
        "Application starting"
    );
}

/// Log configuration information (without sensitive data)
pub fn log_config_info(config: &crate::infrastructure::config::AppConfig) {
    info!(
        server_host = %config.server.host,
        server_port = %config.server.port,
        database_url = "[REDACTED]",
        redis_url = "[REDACTED]",
        jwt_expiration = %config.jwt.expiration,
        file_storage_path = %config.file_storage.path,
        max_file_size = %config.file_storage.max_file_size,
        rate_limiting_enabled = %config.rate_limiting.enabled,
        security_hsts_enabled = %config.security.enable_hsts,
        security_csp_enabled = %config.security.enable_csp,
        "Configuration loaded"
    );
}

/// Log performance metrics
pub fn log_performance_metrics(
    operation: &str,
    duration_ms: u64,
    success: bool,
    additional_fields: Option<serde_json::Value>,
) {
    if success {
        info!(
            operation = %operation,
            duration_ms = %duration_ms,
            success = %success,
            additional_fields = ?additional_fields,
            "Operation completed"
        );
    } else {
        warn!(
            operation = %operation,
            duration_ms = %duration_ms,
            success = %success,
            additional_fields = ?additional_fields,
            "Operation failed"
        );
    }
}

/// Log security events
pub fn log_security_event(
    event_type: &str,
    user_id: Option<uuid::Uuid>,
    ip_address: Option<&str>,
    details: serde_json::Value,
) {
    warn!(
        event_type = %event_type,
        user_id = ?user_id,
        ip_address = ?ip_address,
        details = ?details,
        "Security event detected"
    );
}

/// Log audit events
pub fn log_audit_event(
    action: &str,
    user_id: Option<uuid::Uuid>,
    tenant_id: Option<uuid::Uuid>,
    entity_type: &str,
    entity_id: uuid::Uuid,
    changes: Option<serde_json::Value>,
) {
    info!(
        action = %action,
        user_id = ?user_id,
        tenant_id = ?tenant_id,
        entity_type = %entity_type,
        entity_id = %entity_id,
        changes = ?changes,
        "Audit event"
    );
}

/// Log database operations
pub fn log_database_operation(
    operation: &str,
    table: &str,
    duration_ms: u64,
    rows_affected: Option<u64>,
    success: bool,
) {
    if success {
        info!(
            operation = %operation,
            table = %table,
            duration_ms = %duration_ms,
            rows_affected = ?rows_affected,
            "Database operation completed"
        );
    } else {
        error!(
            operation = %operation,
            table = %table,
            duration_ms = %duration_ms,
            "Database operation failed"
        );
    }
}

/// Log HTTP requests
pub fn log_http_request(
    method: &str,
    path: &str,
    status_code: u16,
    duration_ms: u64,
    user_id: Option<uuid::Uuid>,
    ip_address: Option<&str>,
    user_agent: Option<&str>,
) {
    match status_code {
        200..=299 => info!(
            method = %method,
            path = %path,
            status_code = %status_code,
            duration_ms = %duration_ms,
            user_id = ?user_id,
            ip_address = ?ip_address,
            user_agent = ?user_agent,
            "HTTP request"
        ),
        300..=399 => info!(
            method = %method,
            path = %path,
            status_code = %status_code,
            duration_ms = %duration_ms,
            user_id = ?user_id,
            ip_address = ?ip_address,
            user_agent = ?user_agent,
            "HTTP request"
        ),
        400..=499 => warn!(
            method = %method,
            path = %path,
            status_code = %status_code,
            duration_ms = %duration_ms,
            user_id = ?user_id,
            ip_address = ?ip_address,
            user_agent = ?user_agent,
            "HTTP request"
        ),
        500..=599 => error!(
            method = %method,
            path = %path,
            status_code = %status_code,
            duration_ms = %duration_ms,
            user_id = ?user_id,
            ip_address = ?ip_address,
            user_agent = ?user_agent,
            "HTTP request"
        ),
        _ => info!(
            method = %method,
            path = %path,
            status_code = %status_code,
            duration_ms = %duration_ms,
            user_id = ?user_id,
            ip_address = ?ip_address,
            user_agent = ?user_agent,
            "HTTP request"
        ),
    }
}

/// Log file operations
pub fn log_file_operation(
    operation: &str,
    filename: &str,
    file_size: Option<u64>,
    user_id: Option<uuid::Uuid>,
    success: bool,
    error_message: Option<&str>,
) {
    if success {
        info!(
            operation = %operation,
            filename = %filename,
            file_size = ?file_size,
            user_id = ?user_id,
            "File operation completed"
        );
    } else {
        error!(
            operation = %operation,
            filename = %filename,
            file_size = ?file_size,
            user_id = ?user_id,
            error_message = ?error_message,
            "File operation failed"
        );
    }
}

/// Log business events
pub fn log_business_event(
    event_type: &str,
    user_id: Option<uuid::Uuid>,
    tenant_id: Option<uuid::Uuid>,
    details: serde_json::Value,
) {
    info!(
        event_type = %event_type,
        user_id = ?user_id,
        tenant_id = ?tenant_id,
        details = ?details,
        "Business event"
    );
}

/// Log system health events
pub fn log_health_event(
    component: &str,
    status: &str,
    metrics: Option<serde_json::Value>,
) {
    match status {
        "healthy" => info!(
            component = %component,
            status = %status,
            metrics = ?metrics,
            "Health check passed"
        ),
        "degraded" => warn!(
            component = %component,
            status = %status,
            metrics = ?metrics,
            "Health check degraded"
        ),
        "unhealthy" => error!(
            component = %component,
            status = %status,
            metrics = ?metrics,
            "Health check failed"
        ),
        _ => info!(
            component = %component,
            status = %status,
            metrics = ?metrics,
            "Health check completed"
        ),
    }
}

/// Create a structured log context for request tracing
#[derive(Debug, Clone)]
pub struct LogContext {
    pub request_id: String,
    pub user_id: Option<uuid::Uuid>,
    pub tenant_id: Option<uuid::Uuid>,
    pub session_id: Option<String>,
    pub ip_address: Option<String>,
}

impl LogContext {
    pub fn new(request_id: String) -> Self {
        Self {
            request_id,
            user_id: None,
            tenant_id: None,
            session_id: None,
            ip_address: None,
        }
    }

    pub fn with_user(mut self, user_id: uuid::Uuid) -> Self {
        self.user_id = Some(user_id);
        self
    }

    pub fn with_tenant(mut self, tenant_id: uuid::Uuid) -> Self {
        self.tenant_id = Some(tenant_id);
        self
    }

    pub fn with_session(mut self, session_id: String) -> Self {
        self.session_id = Some(session_id);
        self
    }

    pub fn with_ip(mut self, ip_address: String) -> Self {
        self.ip_address = Some(ip_address);
        self
    }
}

/// Macro for logging with context
#[macro_export]
macro_rules! log_with_context {
    ($level:ident, $context:expr, $($field:ident = $value:expr),* $(,)? ; $message:expr) => {
        tracing::$level!(
            request_id = %$context.request_id,
            user_id = ?$context.user_id,
            tenant_id = ?$context.tenant_id,
            session_id = ?$context.session_id,
            ip_address = ?$context.ip_address,
            $($field = $value,)*
            $message
        );
    };
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_log_context_creation() {
        let context = LogContext::new("req-123".to_string())
            .with_user(uuid::Uuid::new_v4())
            .with_tenant(uuid::Uuid::new_v4())
            .with_session("session-456".to_string())
            .with_ip("***********".to_string());

        assert_eq!(context.request_id, "req-123");
        assert!(context.user_id.is_some());
        assert!(context.tenant_id.is_some());
        assert_eq!(context.session_id, Some("session-456".to_string()));
        assert_eq!(context.ip_address, Some("***********".to_string()));
    }
}
