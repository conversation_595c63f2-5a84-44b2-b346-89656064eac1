use std::sync::Arc;
use std::collections::HashMap;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};

/// Metrics collection service for application monitoring
pub struct MetricsService {
    counters: Arc<RwLock<HashMap<String, u64>>>,
    gauges: Arc<RwLock<HashMap<String, f64>>>,
    histograms: Arc<RwLock<HashMap<String, Vec<f64>>>>,
    timers: Arc<RwLock<HashMap<String, Vec<Duration>>>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MetricValue {
    pub name: String,
    pub value: f64,
    pub metric_type: MetricType,
    pub labels: HashMap<String, String>,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum MetricType {
    Counter,
    Gauge,
    Histogram,
    Timer,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct MetricsSnapshot {
    pub counters: HashMap<String, u64>,
    pub gauges: HashMap<String, f64>,
    pub histograms: HashMap<String, HistogramStats>,
    pub timers: HashMap<String, TimerStats>,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HistogramStats {
    pub count: usize,
    pub min: f64,
    pub max: f64,
    pub mean: f64,
    pub p50: f64,
    pub p95: f64,
    pub p99: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TimerStats {
    pub count: usize,
    pub min_ms: f64,
    pub max_ms: f64,
    pub mean_ms: f64,
    pub p50_ms: f64,
    pub p95_ms: f64,
    pub p99_ms: f64,
}

impl MetricsService {
    pub fn new() -> Self {
        Self {
            counters: Arc::new(RwLock::new(HashMap::new())),
            gauges: Arc::new(RwLock::new(HashMap::new())),
            histograms: Arc::new(RwLock::new(HashMap::new())),
            timers: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Increment a counter metric
    pub async fn increment_counter(&self, name: &str, value: u64) {
        let mut counters = self.counters.write().await;
        *counters.entry(name.to_string()).or_insert(0) += value;
    }

    /// Set a gauge metric
    pub async fn set_gauge(&self, name: &str, value: f64) {
        let mut gauges = self.gauges.write().await;
        gauges.insert(name.to_string(), value);
    }

    /// Record a histogram value
    pub async fn record_histogram(&self, name: &str, value: f64) {
        let mut histograms = self.histograms.write().await;
        histograms.entry(name.to_string()).or_insert_with(Vec::new).push(value);
    }

    /// Record a timer duration
    pub async fn record_timer(&self, name: &str, duration: Duration) {
        let mut timers = self.timers.write().await;
        timers.entry(name.to_string()).or_insert_with(Vec::new).push(duration);
    }

    /// Get current metrics snapshot
    pub async fn get_snapshot(&self) -> MetricsSnapshot {
        let counters = self.counters.read().await.clone();
        let gauges = self.gauges.read().await.clone();
        
        let histograms_raw = self.histograms.read().await;
        let histograms = histograms_raw
            .iter()
            .map(|(name, values)| (name.clone(), calculate_histogram_stats(values)))
            .collect();

        let timers_raw = self.timers.read().await;
        let timers = timers_raw
            .iter()
            .map(|(name, durations)| (name.clone(), calculate_timer_stats(durations)))
            .collect();

        MetricsSnapshot {
            counters,
            gauges,
            histograms,
            timers,
            timestamp: chrono::Utc::now(),
        }
    }

    /// Reset all metrics
    pub async fn reset(&self) {
        self.counters.write().await.clear();
        self.gauges.write().await.clear();
        self.histograms.write().await.clear();
        self.timers.write().await.clear();
    }

    /// Export metrics in Prometheus format
    pub async fn export_prometheus(&self) -> String {
        let snapshot = self.get_snapshot().await;
        let mut output = String::new();

        // Export counters
        for (name, value) in &snapshot.counters {
            output.push_str(&format!("# TYPE {} counter\n", name));
            output.push_str(&format!("{} {}\n", name, value));
        }

        // Export gauges
        for (name, value) in &snapshot.gauges {
            output.push_str(&format!("# TYPE {} gauge\n", name));
            output.push_str(&format!("{} {}\n", name, value));
        }

        // Export histograms
        for (name, stats) in &snapshot.histograms {
            output.push_str(&format!("# TYPE {} histogram\n", name));
            output.push_str(&format!("{}_count {}\n", name, stats.count));
            output.push_str(&format!("{}_min {}\n", name, stats.min));
            output.push_str(&format!("{}_max {}\n", name, stats.max));
            output.push_str(&format!("{}_mean {}\n", name, stats.mean));
            output.push_str(&format!("{}_p50 {}\n", name, stats.p50));
            output.push_str(&format!("{}_p95 {}\n", name, stats.p95));
            output.push_str(&format!("{}_p99 {}\n", name, stats.p99));
        }

        // Export timers
        for (name, stats) in &snapshot.timers {
            output.push_str(&format!("# TYPE {}_duration_ms histogram\n", name));
            output.push_str(&format!("{}_duration_ms_count {}\n", name, stats.count));
            output.push_str(&format!("{}_duration_ms_min {}\n", name, stats.min_ms));
            output.push_str(&format!("{}_duration_ms_max {}\n", name, stats.max_ms));
            output.push_str(&format!("{}_duration_ms_mean {}\n", name, stats.mean_ms));
            output.push_str(&format!("{}_duration_ms_p50 {}\n", name, stats.p50_ms));
            output.push_str(&format!("{}_duration_ms_p95 {}\n", name, stats.p95_ms));
            output.push_str(&format!("{}_duration_ms_p99 {}\n", name, stats.p99_ms));
        }

        output
    }
}

impl Default for MetricsService {
    fn default() -> Self {
        Self::new()
    }
}

/// Calculate histogram statistics
fn calculate_histogram_stats(values: &[f64]) -> HistogramStats {
    if values.is_empty() {
        return HistogramStats {
            count: 0,
            min: 0.0,
            max: 0.0,
            mean: 0.0,
            p50: 0.0,
            p95: 0.0,
            p99: 0.0,
        };
    }

    let mut sorted_values = values.to_vec();
    sorted_values.sort_by(|a, b| a.partial_cmp(b).unwrap());

    let count = sorted_values.len();
    let min = sorted_values[0];
    let max = sorted_values[count - 1];
    let mean = sorted_values.iter().sum::<f64>() / count as f64;

    let p50 = percentile(&sorted_values, 0.5);
    let p95 = percentile(&sorted_values, 0.95);
    let p99 = percentile(&sorted_values, 0.99);

    HistogramStats {
        count,
        min,
        max,
        mean,
        p50,
        p95,
        p99,
    }
}

/// Calculate timer statistics
fn calculate_timer_stats(durations: &[Duration]) -> TimerStats {
    if durations.is_empty() {
        return TimerStats {
            count: 0,
            min_ms: 0.0,
            max_ms: 0.0,
            mean_ms: 0.0,
            p50_ms: 0.0,
            p95_ms: 0.0,
            p99_ms: 0.0,
        };
    }

    let values_ms: Vec<f64> = durations
        .iter()
        .map(|d| d.as_secs_f64() * 1000.0)
        .collect();

    let stats = calculate_histogram_stats(&values_ms);

    TimerStats {
        count: stats.count,
        min_ms: stats.min,
        max_ms: stats.max,
        mean_ms: stats.mean,
        p50_ms: stats.p50,
        p95_ms: stats.p95,
        p99_ms: stats.p99,
    }
}

/// Calculate percentile from sorted values
fn percentile(sorted_values: &[f64], p: f64) -> f64 {
    if sorted_values.is_empty() {
        return 0.0;
    }

    let index = (p * (sorted_values.len() - 1) as f64).round() as usize;
    sorted_values[index.min(sorted_values.len() - 1)]
}

/// Timer utility for measuring operation duration
pub struct Timer {
    start: Instant,
    name: String,
    metrics: Arc<MetricsService>,
}

impl Timer {
    pub fn new(name: String, metrics: Arc<MetricsService>) -> Self {
        Self {
            start: Instant::now(),
            name,
            metrics,
        }
    }

    pub async fn finish(self) -> Duration {
        let duration = self.start.elapsed();
        self.metrics.record_timer(&self.name, duration).await;
        duration
    }
}

/// Macro for timing operations
#[macro_export]
macro_rules! time_operation {
    ($metrics:expr, $name:expr, $operation:expr) => {{
        let timer = Timer::new($name.to_string(), $metrics.clone());
        let result = $operation;
        timer.finish().await;
        result
    }};
}

/// Application-specific metrics
pub struct ApplicationMetrics {
    pub metrics: Arc<MetricsService>,
}

impl ApplicationMetrics {
    pub fn new(metrics: Arc<MetricsService>) -> Self {
        Self { metrics }
    }

    /// Record HTTP request metrics
    pub async fn record_http_request(&self, method: &str, path: &str, status_code: u16, duration: Duration) {
        let metric_name = format!("http_requests_total_{}_{}", method.to_lowercase(), status_code);
        self.metrics.increment_counter(&metric_name, 1).await;
        
        let duration_name = format!("http_request_duration_{}", method.to_lowercase());
        self.metrics.record_timer(&duration_name, duration).await;
    }

    /// Record database operation metrics
    pub async fn record_database_operation(&self, operation: &str, table: &str, duration: Duration, success: bool) {
        let metric_name = format!("db_operations_total_{}_{}", operation, if success { "success" } else { "error" });
        self.metrics.increment_counter(&metric_name, 1).await;
        
        let duration_name = format!("db_operation_duration_{}_{}", operation, table);
        self.metrics.record_timer(&duration_name, duration).await;
    }

    /// Record file operation metrics
    pub async fn record_file_operation(&self, operation: &str, file_size: Option<u64>, success: bool) {
        let metric_name = format!("file_operations_total_{}_{}", operation, if success { "success" } else { "error" });
        self.metrics.increment_counter(&metric_name, 1).await;
        
        if let Some(size) = file_size {
            let size_name = format!("file_size_bytes_{}", operation);
            self.metrics.record_histogram(&size_name, size as f64).await;
        }
    }

    /// Record authentication metrics
    pub async fn record_auth_event(&self, event_type: &str, success: bool) {
        let metric_name = format!("auth_events_total_{}_{}", event_type, if success { "success" } else { "failure" });
        self.metrics.increment_counter(&metric_name, 1).await;
    }

    /// Record business metrics
    pub async fn record_business_event(&self, event_type: &str, tenant_id: uuid::Uuid) {
        let metric_name = format!("business_events_total_{}", event_type);
        self.metrics.increment_counter(&metric_name, 1).await;
        
        // Record per-tenant metrics
        let tenant_metric = format!("tenant_events_total_{}", tenant_id);
        self.metrics.increment_counter(&tenant_metric, 1).await;
    }

    /// Update system health metrics
    pub async fn update_health_metrics(&self, component: &str, healthy: bool, response_time: Option<Duration>) {
        let health_metric = format!("health_status_{}", component);
        self.metrics.set_gauge(&health_metric, if healthy { 1.0 } else { 0.0 }).await;
        
        if let Some(duration) = response_time {
            let response_time_metric = format!("health_response_time_{}", component);
            self.metrics.record_timer(&response_time_metric, duration).await;
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_metrics_service() {
        let metrics = MetricsService::new();
        
        // Test counter
        metrics.increment_counter("test_counter", 5).await;
        metrics.increment_counter("test_counter", 3).await;
        
        // Test gauge
        metrics.set_gauge("test_gauge", 42.5).await;
        
        // Test histogram
        metrics.record_histogram("test_histogram", 1.0).await;
        metrics.record_histogram("test_histogram", 2.0).await;
        metrics.record_histogram("test_histogram", 3.0).await;
        
        // Test timer
        metrics.record_timer("test_timer", Duration::from_millis(100)).await;
        metrics.record_timer("test_timer", Duration::from_millis(200)).await;
        
        let snapshot = metrics.get_snapshot().await;
        
        assert_eq!(snapshot.counters.get("test_counter"), Some(&8));
        assert_eq!(snapshot.gauges.get("test_gauge"), Some(&42.5));
        assert_eq!(snapshot.histograms.get("test_histogram").unwrap().count, 3);
        assert_eq!(snapshot.timers.get("test_timer").unwrap().count, 2);
    }

    #[test]
    fn test_percentile_calculation() {
        let values = vec![1.0, 2.0, 3.0, 4.0, 5.0];
        assert_eq!(percentile(&values, 0.5), 3.0);
        assert_eq!(percentile(&values, 0.95), 5.0);
    }
}
