use std::path::Path;
use std::collections::hash_map::DefaultHasher;
use std::hash::{Hash, Hasher};
use tracing::warn;
use uuid::Uuid;

use crate::error::{AppError, AppResult};

/// File security service for validating and securing file uploads
pub struct FileSecurityService {
    config: FileSecurityConfig,
    virus_scanner: Option<Box<dyn VirusScanner>>,
}

#[derive(Debug, Clone)]
pub struct FileSecurityConfig {
    pub max_file_size: u64,
    pub allowed_mime_types: Vec<String>,
    pub allowed_extensions: Vec<String>,
    pub blocked_extensions: Vec<String>,
    pub enable_virus_scanning: bool,
    pub enable_content_validation: bool,
    pub quarantine_suspicious_files: bool,
    pub max_files_per_upload: u32,
    pub scan_timeout_seconds: u64,
}

impl Default for FileSecurityConfig {
    fn default() -> Self {
        Self {
            max_file_size: 100 * 1024 * 1024, // 100MB
            allowed_mime_types: vec![
                "application/pdf".to_string(),
                "application/msword".to_string(),
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document".to_string(),
                "application/vnd.ms-excel".to_string(),
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet".to_string(),
                "image/jpeg".to_string(),
                "image/png".to_string(),
                "image/gif".to_string(),
                "text/plain".to_string(),
                "text/csv".to_string(),
            ],
            allowed_extensions: vec![
                "pdf".to_string(), "doc".to_string(), "docx".to_string(),
                "xls".to_string(), "xlsx".to_string(), "png".to_string(),
                "jpg".to_string(), "jpeg".to_string(), "gif".to_string(),
                "txt".to_string(), "csv".to_string(),
            ],
            blocked_extensions: vec![
                "exe".to_string(), "bat".to_string(), "cmd".to_string(),
                "com".to_string(), "pif".to_string(), "scr".to_string(),
                "vbs".to_string(), "js".to_string(), "jar".to_string(),
                "sh".to_string(), "ps1".to_string(), "php".to_string(),
                "asp".to_string(), "aspx".to_string(), "jsp".to_string(),
            ],
            enable_virus_scanning: true,
            enable_content_validation: true,
            quarantine_suspicious_files: true,
            max_files_per_upload: 10,
            scan_timeout_seconds: 30,
        }
    }
}

#[derive(Debug, Clone)]
pub struct FileValidationResult {
    pub is_valid: bool,
    pub file_hash: String,
    pub detected_mime_type: String,
    pub file_size: u64,
    pub security_issues: Vec<SecurityIssue>,
    pub scan_results: Option<VirusScanResult>,
}

#[derive(Debug, Clone)]
pub struct SecurityIssue {
    pub severity: SecuritySeverity,
    pub issue_type: String,
    pub description: String,
    pub recommendation: String,
}

#[derive(Debug, Clone)]
pub enum SecuritySeverity {
    Low,
    Medium,
    High,
    Critical,
}

#[derive(Debug, Clone)]
pub struct VirusScanResult {
    pub is_clean: bool,
    pub threats_detected: Vec<String>,
    pub scan_engine: String,
    pub scan_duration_ms: u64,
}

/// Trait for virus scanning implementations
#[async_trait::async_trait]
pub trait VirusScanner: Send + Sync {
    async fn scan_file(&self, file_content: &[u8], filename: &str) -> AppResult<VirusScanResult>;
}

impl FileSecurityService {
    pub fn new(config: FileSecurityConfig) -> Self {
        let virus_scanner = if config.enable_virus_scanning {
            Some(Box::new(ClamAVScanner::new()) as Box<dyn VirusScanner>)
        } else {
            None
        };

        Self {
            config,
            virus_scanner,
        }
    }

    /// Comprehensive file validation and security check
    pub async fn validate_file(
        &self,
        filename: &str,
        content: &[u8],
        declared_mime_type: Option<&str>,
    ) -> AppResult<FileValidationResult> {
        let mut security_issues = Vec::new();
        let mut is_valid = true;

        // Calculate file hash
        let file_hash = self.calculate_file_hash(content);
        let file_size = content.len() as u64;

        // 1. File size validation
        if file_size > self.config.max_file_size {
            security_issues.push(SecurityIssue {
                severity: SecuritySeverity::High,
                issue_type: "file_size_exceeded".to_string(),
                description: format!("File size {} bytes exceeds maximum allowed {} bytes", 
                                   file_size, self.config.max_file_size),
                recommendation: "Reduce file size or contact administrator".to_string(),
            });
            is_valid = false;
        }

        // 2. File extension validation
        let extension = self.extract_file_extension(filename);
        if self.config.blocked_extensions.contains(&extension.to_lowercase()) {
            security_issues.push(SecurityIssue {
                severity: SecuritySeverity::Critical,
                issue_type: "blocked_extension".to_string(),
                description: format!("File extension '{}' is blocked for security reasons", extension),
                recommendation: "Use a different file format".to_string(),
            });
            is_valid = false;
        }

        if !self.config.allowed_extensions.contains(&extension.to_lowercase()) {
            security_issues.push(SecurityIssue {
                severity: SecuritySeverity::Medium,
                issue_type: "extension_not_allowed".to_string(),
                description: format!("File extension '{}' is not in the allowed list", extension),
                recommendation: "Use an allowed file format".to_string(),
            });
            is_valid = false;
        }

        // 3. MIME type detection and validation
        let detected_mime_type = self.detect_mime_type(content, filename);
        
        if !self.config.allowed_mime_types.contains(&detected_mime_type) {
            security_issues.push(SecurityIssue {
                severity: SecuritySeverity::Medium,
                issue_type: "mime_type_not_allowed".to_string(),
                description: format!("Detected MIME type '{}' is not allowed", detected_mime_type),
                recommendation: "Convert file to an allowed format".to_string(),
            });
            is_valid = false;
        }

        // 4. MIME type spoofing detection
        if let Some(declared) = declared_mime_type {
            if declared != detected_mime_type {
                security_issues.push(SecurityIssue {
                    severity: SecuritySeverity::High,
                    issue_type: "mime_type_mismatch".to_string(),
                    description: format!("Declared MIME type '{}' doesn't match detected '{}'", 
                                       declared, detected_mime_type),
                    recommendation: "File may be malicious or corrupted".to_string(),
                });
                
                if self.config.quarantine_suspicious_files {
                    is_valid = false;
                }
            }
        }

        // 5. Content validation
        if self.config.enable_content_validation {
            let content_issues = self.validate_file_content(content, &detected_mime_type, filename).await?;
            security_issues.extend(content_issues);
        }

        // 6. Virus scanning
        let scan_results = if let Some(scanner) = &self.virus_scanner {
            match scanner.scan_file(content, filename).await {
                Ok(result) => {
                    if !result.is_clean {
                        security_issues.push(SecurityIssue {
                            severity: SecuritySeverity::Critical,
                            issue_type: "virus_detected".to_string(),
                            description: format!("Virus/malware detected: {}", 
                                               result.threats_detected.join(", ")),
                            recommendation: "File has been quarantined".to_string(),
                        });
                        is_valid = false;
                    }
                    Some(result)
                }
                Err(e) => {
                    warn!("Virus scan failed: {}", e);
                    security_issues.push(SecurityIssue {
                        severity: SecuritySeverity::Medium,
                        issue_type: "scan_failed".to_string(),
                        description: "Virus scan could not be completed".to_string(),
                        recommendation: "File upload may be restricted".to_string(),
                    });
                    None
                }
            }
        } else {
            None
        };

        // 7. Check for suspicious patterns
        let pattern_issues = self.check_suspicious_patterns(content, filename);
        security_issues.extend(pattern_issues);

        Ok(FileValidationResult {
            is_valid,
            file_hash,
            detected_mime_type,
            file_size,
            security_issues,
            scan_results,
        })
    }

    /// Calculate hash of file content (using simple hash for now)
    fn calculate_file_hash(&self, content: &[u8]) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        content.hash(&mut hasher);
        format!("{:x}", hasher.finish())
    }

    /// Extract file extension from filename
    fn extract_file_extension(&self, filename: &str) -> String {
        Path::new(filename)
            .extension()
            .and_then(|ext| ext.to_str())
            .unwrap_or("")
            .to_lowercase()
    }

    /// Detect MIME type from file content
    fn detect_mime_type(&self, content: &[u8], filename: &str) -> String {
        // Simple MIME type detection based on file signatures
        if content.len() < 4 {
            return "application/octet-stream".to_string();
        }

        // Check magic numbers/file signatures
        match &content[0..4] {
            [0x25, 0x50, 0x44, 0x46] => "application/pdf".to_string(), // PDF
            [0x89, 0x50, 0x4E, 0x47] => "image/png".to_string(),       // PNG
            [0xFF, 0xD8, 0xFF, _] => "image/jpeg".to_string(),         // JPEG
            [0x47, 0x49, 0x46, 0x38] => "image/gif".to_string(),       // GIF
            [0x50, 0x4B, 0x03, 0x04] | [0x50, 0x4B, 0x05, 0x06] => {
                // ZIP-based formats (Office documents)
                let extension = self.extract_file_extension(filename);
                match extension.as_str() {
                    "docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document".to_string(),
                    "xlsx" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet".to_string(),
                    _ => "application/zip".to_string(),
                }
            }
            _ => {
                // Check for text files
                if content.iter().all(|&b| b.is_ascii() && (b.is_ascii_graphic() || b.is_ascii_whitespace())) {
                    "text/plain".to_string()
                } else {
                    "application/octet-stream".to_string()
                }
            }
        }
    }

    /// Validate file content for specific file types
    async fn validate_file_content(
        &self,
        content: &[u8],
        mime_type: &str,
        filename: &str,
    ) -> AppResult<Vec<SecurityIssue>> {
        let mut issues = Vec::new();

        match mime_type {
            "application/pdf" => {
                issues.extend(self.validate_pdf_content(content)?);
            }
            "image/jpeg" | "image/png" | "image/gif" => {
                issues.extend(self.validate_image_content(content, mime_type)?);
            }
            "text/plain" | "text/csv" => {
                issues.extend(self.validate_text_content(content)?);
            }
            _ => {
                // Generic validation for other file types
                issues.extend(self.validate_generic_content(content)?);
            }
        }

        Ok(issues)
    }

    /// Validate PDF content
    fn validate_pdf_content(&self, content: &[u8]) -> AppResult<Vec<SecurityIssue>> {
        let mut issues = Vec::new();

        // Check for PDF header
        if !content.starts_with(b"%PDF-") {
            issues.push(SecurityIssue {
                severity: SecuritySeverity::High,
                issue_type: "invalid_pdf_header".to_string(),
                description: "File claims to be PDF but doesn't have valid PDF header".to_string(),
                recommendation: "File may be corrupted or malicious".to_string(),
            });
        }

        // Check for suspicious JavaScript in PDF
        let content_str = String::from_utf8_lossy(content).to_lowercase();
        if content_str.contains("/javascript") || content_str.contains("/js") {
            issues.push(SecurityIssue {
                severity: SecuritySeverity::High,
                issue_type: "pdf_javascript_detected".to_string(),
                description: "PDF contains JavaScript which could be malicious".to_string(),
                recommendation: "Consider using a PDF without JavaScript".to_string(),
            });
        }

        Ok(issues)
    }

    /// Validate image content
    fn validate_image_content(&self, content: &[u8], mime_type: &str) -> AppResult<Vec<SecurityIssue>> {
        let mut issues = Vec::new();

        // Check for embedded scripts in image metadata
        let content_str = String::from_utf8_lossy(content).to_lowercase();
        if content_str.contains("<script") || content_str.contains("javascript:") {
            issues.push(SecurityIssue {
                severity: SecuritySeverity::High,
                issue_type: "image_script_detected".to_string(),
                description: "Image contains embedded scripts".to_string(),
                recommendation: "Image may contain malicious code".to_string(),
            });
        }

        // Basic image format validation
        match mime_type {
            "image/jpeg" => {
                if !content.starts_with(&[0xFF, 0xD8, 0xFF]) {
                    issues.push(SecurityIssue {
                        severity: SecuritySeverity::Medium,
                        issue_type: "invalid_jpeg_header".to_string(),
                        description: "Invalid JPEG file header".to_string(),
                        recommendation: "File may be corrupted".to_string(),
                    });
                }
            }
            "image/png" => {
                if !content.starts_with(&[0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A]) {
                    issues.push(SecurityIssue {
                        severity: SecuritySeverity::Medium,
                        issue_type: "invalid_png_header".to_string(),
                        description: "Invalid PNG file header".to_string(),
                        recommendation: "File may be corrupted".to_string(),
                    });
                }
            }
            _ => {}
        }

        Ok(issues)
    }

    /// Validate text content
    fn validate_text_content(&self, content: &[u8]) -> AppResult<Vec<SecurityIssue>> {
        let mut issues = Vec::new();

        // Check for suspicious scripts
        let content_str = String::from_utf8_lossy(content).to_lowercase();
        let suspicious_patterns = [
            "<script", "javascript:", "vbscript:", "onload=", "onerror=",
            "eval(", "document.cookie", "window.location",
        ];

        for pattern in &suspicious_patterns {
            if content_str.contains(pattern) {
                issues.push(SecurityIssue {
                    severity: SecuritySeverity::Medium,
                    issue_type: "suspicious_script_pattern".to_string(),
                    description: format!("Text file contains suspicious pattern: {}", pattern),
                    recommendation: "Review file content for malicious code".to_string(),
                });
            }
        }

        Ok(issues)
    }

    /// Generic content validation
    fn validate_generic_content(&self, content: &[u8]) -> AppResult<Vec<SecurityIssue>> {
        let mut issues = Vec::new();

        // Check for null bytes (potential binary in text file)
        if content.contains(&0) {
            issues.push(SecurityIssue {
                severity: SecuritySeverity::Low,
                issue_type: "null_bytes_detected".to_string(),
                description: "File contains null bytes".to_string(),
                recommendation: "File may be binary or corrupted".to_string(),
            });
        }

        Ok(issues)
    }

    /// Check for suspicious patterns in file content
    fn check_suspicious_patterns(&self, content: &[u8], filename: &str) -> Vec<SecurityIssue> {
        let mut issues = Vec::new();

        // Check for double extensions (e.g., file.pdf.exe)
        let parts: Vec<&str> = filename.split('.').collect();
        if parts.len() > 2 {
            issues.push(SecurityIssue {
                severity: SecuritySeverity::Medium,
                issue_type: "double_extension".to_string(),
                description: "Filename has multiple extensions".to_string(),
                recommendation: "May be an attempt to hide file type".to_string(),
            });
        }

        // Check for very long filenames
        if filename.len() > 255 {
            issues.push(SecurityIssue {
                severity: SecuritySeverity::Low,
                issue_type: "long_filename".to_string(),
                description: "Filename is unusually long".to_string(),
                recommendation: "Consider shortening the filename".to_string(),
            });
        }

        // Check for suspicious filename characters
        let suspicious_chars = ['<', '>', ':', '"', '|', '?', '*', '\0'];
        if filename.chars().any(|c| suspicious_chars.contains(&c)) {
            issues.push(SecurityIssue {
                severity: SecuritySeverity::Medium,
                issue_type: "suspicious_filename_chars".to_string(),
                description: "Filename contains suspicious characters".to_string(),
                recommendation: "Use only alphanumeric characters and common symbols".to_string(),
            });
        }

        issues
    }

    /// Generate a safe filename
    pub fn generate_safe_filename(&self, original_name: &str) -> String {
        let extension = self.extract_file_extension(original_name);
        let base_name = Path::new(original_name)
            .file_stem()
            .and_then(|stem| stem.to_str())
            .unwrap_or("file");

        // Sanitize the base name
        let safe_base: String = base_name
            .chars()
            .filter(|c| c.is_alphanumeric() || *c == '-' || *c == '_')
            .take(100) // Limit length
            .collect();

        let safe_base = if safe_base.is_empty() {
            "file".to_string()
        } else {
            safe_base
        };

        // Add timestamp and UUID for uniqueness
        let timestamp = chrono::Utc::now().format("%Y%m%d_%H%M%S");
        let uuid_string = Uuid::new_v4().to_string();
        let uuid_short = uuid_string.split('-').next().unwrap();

        if extension.is_empty() {
            format!("{}_{}{}", safe_base, timestamp, uuid_short)
        } else {
            format!("{}_{}_{}.{}", safe_base, timestamp, uuid_short, extension)
        }
    }
}

/// ClamAV virus scanner implementation
pub struct ClamAVScanner {
    socket_path: String,
}

impl ClamAVScanner {
    pub fn new() -> Self {
        Self {
            socket_path: "/var/run/clamav/clamd.ctl".to_string(),
        }
    }
}

#[async_trait::async_trait]
impl VirusScanner for ClamAVScanner {
    async fn scan_file(&self, file_content: &[u8], filename: &str) -> AppResult<VirusScanResult> {
        let start_time = std::time::Instant::now();
        
        // For now, implement a mock scanner
        // In production, this would connect to ClamAV daemon
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
        
        let scan_duration_ms = start_time.elapsed().as_millis() as u64;
        
        // Mock virus detection for demonstration
        let is_clean = !filename.to_lowercase().contains("virus") && 
                      !filename.to_lowercase().contains("malware");
        
        let threats_detected = if !is_clean {
            vec!["Test.Virus.Detected".to_string()]
        } else {
            vec![]
        };

        Ok(VirusScanResult {
            is_clean,
            threats_detected,
            scan_engine: "ClamAV-Mock".to_string(),
            scan_duration_ms,
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_extract_file_extension() {
        let service = FileSecurityService::new(FileSecurityConfig::default());
        
        assert_eq!(service.extract_file_extension("test.pdf"), "pdf");
        assert_eq!(service.extract_file_extension("test.PDF"), "pdf");
        assert_eq!(service.extract_file_extension("test"), "");
        assert_eq!(service.extract_file_extension("test.tar.gz"), "gz");
    }

    #[test]
    fn test_generate_safe_filename() {
        let service = FileSecurityService::new(FileSecurityConfig::default());
        
        let safe_name = service.generate_safe_filename("test file!@#$.pdf");
        assert!(safe_name.contains("test_file"));
        assert!(safe_name.ends_with(".pdf"));
        assert!(!safe_name.contains("!@#$"));
    }

    #[test]
    fn test_detect_mime_type() {
        let service = FileSecurityService::new(FileSecurityConfig::default());
        
        // PDF signature
        let pdf_content = b"%PDF-1.4\n";
        assert_eq!(service.detect_mime_type(pdf_content, "test.pdf"), "application/pdf");
        
        // PNG signature
        let png_content = b"\x89PNG\r\n\x1a\n";
        assert_eq!(service.detect_mime_type(png_content, "test.png"), "image/png");
    }
}
