use std::sync::Arc;
use axum::{
    extract::{Request, State},
    http::StatusCode,
    middleware::Next,
    response::Response,
    Extension,
};
use tracing::{error, warn, info};
use std::collections::HashMap;

use crate::error::{AppError, ErrorContext};
use crate::infrastructure::monitoring::ErrorMonitoringService;
use crate::domain::services::RequestContext;

/// Error handling middleware state
#[derive(Clone)]
pub struct ErrorHandlingState {
    pub error_monitor: Arc<ErrorMonitoringService>,
    pub enable_detailed_errors: bool,
}

/// Global error handling middleware
pub async fn error_handling_middleware(
    State(state): State<ErrorHandlingState>,
    request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    let start_time = std::time::Instant::now();
    
    // Extract request context for error reporting
    let request_id = request.headers()
        .get("x-request-id")
        .and_then(|v| v.to_str().ok())
        .unwrap_or("unknown")
        .to_string();
    
    let method = request.method().to_string();
    let uri = request.uri().to_string();
    
    // Extract user context if available
    let user_id = request.extensions().get::<uuid::Uuid>().copied();
    let tenant_id = request.extensions().get::<uuid::Uuid>().copied();
    
    // Process the request
    let response = next.run(request).await;
    let duration = start_time.elapsed();
    
    // Check if response indicates an error
    let status_code = response.status();
    if status_code.is_client_error() || status_code.is_server_error() {
        // Create error context
        let mut context = HashMap::new();
        context.insert("request_id".to_string(), request_id.clone());
        context.insert("method".to_string(), method);
        context.insert("uri".to_string(), uri);
        context.insert("duration_ms".to_string(), duration.as_millis().to_string());
        
        if let Some(uid) = user_id {
            context.insert("user_id".to_string(), uid.to_string());
        }
        if let Some(tid) = tenant_id {
            context.insert("tenant_id".to_string(), tid.to_string());
        }
        
        // Create appropriate error based on status code
        let app_error = match status_code {
            StatusCode::BAD_REQUEST => AppError::bad_request("Bad request"),
            StatusCode::UNAUTHORIZED => AppError::authentication("Unauthorized"),
            StatusCode::FORBIDDEN => AppError::authorization("Forbidden"),
            StatusCode::NOT_FOUND => AppError::not_found("Resource not found"),
            StatusCode::CONFLICT => AppError::conflict("Conflict"),
            StatusCode::UNPROCESSABLE_ENTITY => AppError::validation("Validation failed"),
            StatusCode::TOO_MANY_REQUESTS => AppError::RateLimitExceeded,
            StatusCode::INTERNAL_SERVER_ERROR => AppError::internal("Internal server error"),
            StatusCode::BAD_GATEWAY => AppError::external_service("upstream", "Bad gateway"),
            StatusCode::SERVICE_UNAVAILABLE => AppError::service_unavailable("Service unavailable"),
            StatusCode::GATEWAY_TIMEOUT => AppError::timeout("Gateway timeout"),
            _ => AppError::internal("Unknown error"),
        };
        
        // Record error in monitoring system
        state.error_monitor.record_error(&app_error, Some(context));
        
        // Log the error
        if status_code.is_server_error() {
            error!(
                request_id = %request_id,
                status_code = %status_code.as_u16(),
                duration_ms = %duration.as_millis(),
                user_id = ?user_id,
                "Server error occurred"
            );
        } else {
            warn!(
                request_id = %request_id,
                status_code = %status_code.as_u16(),
                duration_ms = %duration.as_millis(),
                user_id = ?user_id,
                "Client error occurred"
            );
        }
    } else {
        // Log successful requests (debug level)
        info!(
            request_id = %request_id,
            status_code = %status_code.as_u16(),
            duration_ms = %duration.as_millis(),
            "Request completed successfully"
        );
    }
    
    Ok(response)
}

/// Panic handler middleware to catch and handle panics gracefully
pub async fn panic_handler_middleware(
    State(state): State<ErrorHandlingState>,
    request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    // Set up panic hook to capture panics
    let original_hook = std::panic::take_hook();
    let error_monitor = state.error_monitor.clone();
    
    std::panic::set_hook(Box::new(move |panic_info| {
        let panic_message = if let Some(s) = panic_info.payload().downcast_ref::<&str>() {
            s.to_string()
        } else if let Some(s) = panic_info.payload().downcast_ref::<String>() {
            s.clone()
        } else {
            "Unknown panic".to_string()
        };
        
        let location = panic_info.location()
            .map(|loc| format!("{}:{}:{}", loc.file(), loc.line(), loc.column()))
            .unwrap_or_else(|| "unknown location".to_string());
        
        error!("Panic occurred: {} at {}", panic_message, location);
        
        // Record panic as critical error
        let panic_error = AppError::internal(format!("Panic: {} at {}", panic_message, location));
        let mut context = HashMap::new();
        context.insert("panic_location".to_string(), location);
        context.insert("panic_message".to_string(), panic_message);
        
        error_monitor.record_error(&panic_error, Some(context));
    }));
    
    // Process request with panic protection
    let result = std::panic::catch_unwind(std::panic::AssertUnwindSafe(|| {
        // This is a simplified approach - in practice, you'd need async-compatible panic handling
        // For now, we'll just process normally and rely on the panic hook
        next.run(request)
    }));
    
    // Restore original panic hook
    std::panic::set_hook(original_hook);
    
    match result {
        Ok(future) => {
            // Await the future normally
            Ok(future.await)
        }
        Err(_) => {
            // Panic occurred - return 500 error
            error!("Request handler panicked");
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

/// Timeout middleware to prevent long-running requests
pub async fn timeout_middleware(
    request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    let timeout_duration = std::time::Duration::from_secs(30); // 30 second timeout
    
    match tokio::time::timeout(timeout_duration, next.run(request)).await {
        Ok(response) => Ok(response),
        Err(_) => {
            error!("Request timed out after {} seconds", timeout_duration.as_secs());
            Err(StatusCode::REQUEST_TIMEOUT)
        }
    }
}

/// Request size limit middleware
pub async fn request_size_limit_middleware(
    request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    const MAX_REQUEST_SIZE: u64 = 100 * 1024 * 1024; // 100MB
    
    if let Some(content_length) = request.headers().get("content-length") {
        if let Ok(length_str) = content_length.to_str() {
            if let Ok(length) = length_str.parse::<u64>() {
                if length > MAX_REQUEST_SIZE {
                    warn!("Request size {} exceeds limit {}", length, MAX_REQUEST_SIZE);
                    return Err(StatusCode::PAYLOAD_TOO_LARGE);
                }
            }
        }
    }
    
    Ok(next.run(request).await)
}

/// Create error context from request
pub fn create_error_context(
    request_id: &str,
    user_id: Option<uuid::Uuid>,
    tenant_id: Option<uuid::Uuid>,
    operation: Option<&str>,
    resource: Option<&str>,
) -> ErrorContext {
    ErrorContext {
        user_id,
        tenant_id,
        operation: operation.map(|s| s.to_string()),
        resource: resource.map(|s| s.to_string()),
        correlation_id: Some(request_id.to_string()),
        stack_trace: None, // Could be populated in debug mode
    }
}

/// Helper function to extract error details from response
pub fn extract_error_from_response(response: &Response) -> Option<AppError> {
    match response.status() {
        StatusCode::BAD_REQUEST => Some(AppError::bad_request("Bad request")),
        StatusCode::UNAUTHORIZED => Some(AppError::authentication("Unauthorized")),
        StatusCode::FORBIDDEN => Some(AppError::authorization("Forbidden")),
        StatusCode::NOT_FOUND => Some(AppError::not_found("Not found")),
        StatusCode::CONFLICT => Some(AppError::conflict("Conflict")),
        StatusCode::UNPROCESSABLE_ENTITY => Some(AppError::validation("Validation error")),
        StatusCode::TOO_MANY_REQUESTS => Some(AppError::RateLimitExceeded),
        StatusCode::INTERNAL_SERVER_ERROR => Some(AppError::internal("Internal server error")),
        StatusCode::BAD_GATEWAY => Some(AppError::external_service("upstream", "Bad gateway")),
        StatusCode::SERVICE_UNAVAILABLE => Some(AppError::service_unavailable("Service unavailable")),
        StatusCode::GATEWAY_TIMEOUT => Some(AppError::timeout("Gateway timeout")),
        _ => None,
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use axum::http::StatusCode;

    #[test]
    fn test_create_error_context() {
        let context = create_error_context(
            "req-123",
            Some(uuid::Uuid::new_v4()),
            Some(uuid::Uuid::new_v4()),
            Some("test_operation"),
            Some("test_resource"),
        );
        
        assert_eq!(context.correlation_id, Some("req-123".to_string()));
        assert_eq!(context.operation, Some("test_operation".to_string()));
        assert_eq!(context.resource, Some("test_resource".to_string()));
        assert!(context.user_id.is_some());
        assert!(context.tenant_id.is_some());
    }

    #[test]
    fn test_extract_error_from_response() {
        let response = Response::builder()
            .status(StatusCode::NOT_FOUND)
            .body(())
            .unwrap();
        
        let error = extract_error_from_response(&response);
        assert!(error.is_some());
        
        if let Some(err) = error {
            assert_eq!(err.status_code(), StatusCode::NOT_FOUND);
        }
    }
}
