pub mod audit;
pub mod security;
pub mod rate_limit;
pub mod error_handling;

pub use audit::*;
pub use security::*;
pub use rate_limit::*;
pub use error_handling::*;

use std::sync::Arc;
use axum::{
    extract::State,
    middleware,
    Router,
};

use crate::infrastructure::config::{SecurityConfig, RateLimitingConfig};
use crate::domain::services::AuditService;

/// Security middleware state
#[derive(Clone)]
pub struct SecurityMiddlewareState {
    pub security_config: SecurityConfig,
    pub rate_limiting_config: RateLimitingConfig,
    pub audit_service: Arc<AuditService>,
    pub rate_limiters: std::collections::HashMap<String, Arc<InMemoryRateLimiter>>,
}

impl SecurityMiddlewareState {
    pub fn new(
        security_config: SecurityConfig,
        rate_limiting_config: RateLimitingConfig,
        audit_service: Arc<AuditService>,
    ) -> Self {
        let rate_limiters = if rate_limiting_config.enabled {
            create_configured_rate_limiters(&rate_limiting_config)
        } else {
            std::collections::HashMap::new()
        };

        Self {
            security_config,
            rate_limiting_config,
            audit_service,
            rate_limiters,
        }
    }
}

/// Create rate limiters based on configuration
fn create_configured_rate_limiters(config: &RateLimitingConfig) -> std::collections::HashMap<String, Arc<InMemoryRateLimiter>> {
    let mut limiters = std::collections::HashMap::new();
    
    // General API rate limit
    limiters.insert(
        "general".to_string(),
        Arc::new(InMemoryRateLimiter::new(RateLimitConfig {
            max_requests: config.general_max_requests,
            window_duration: std::time::Duration::from_secs(config.general_window_seconds),
            per_ip: config.per_ip,
            per_user: config.per_user,
        }))
    );
    
    // Authentication endpoints - stricter limits
    limiters.insert(
        "auth".to_string(),
        Arc::new(InMemoryRateLimiter::new(RateLimitConfig {
            max_requests: config.auth_max_requests,
            window_duration: std::time::Duration::from_secs(config.auth_window_seconds),
            per_ip: config.per_ip,
            per_user: false, // Auth endpoints don't have user context yet
        }))
    );
    
    // File upload endpoints - very strict limits
    limiters.insert(
        "upload".to_string(),
        Arc::new(InMemoryRateLimiter::new(RateLimitConfig {
            max_requests: config.upload_max_requests,
            window_duration: std::time::Duration::from_secs(config.upload_window_seconds),
            per_ip: config.per_ip,
            per_user: config.per_user,
        }))
    );
    
    // Export endpoints - moderate limits
    limiters.insert(
        "export".to_string(),
        Arc::new(InMemoryRateLimiter::new(RateLimitConfig {
            max_requests: config.export_max_requests,
            window_duration: std::time::Duration::from_secs(config.export_window_seconds),
            per_ip: config.per_ip,
            per_user: config.per_user,
        }))
    );
    
    limiters
}

/// Apply security middleware stack to a router
pub fn apply_security_middleware(
    router: Router,
    state: SecurityMiddlewareState,
) -> Router {
    let security_config = state.security_config.clone();
    let audit_state = AuditMiddlewareState {
        audit_service: state.audit_service.clone(),
        config: crate::infrastructure::config::AppConfig {
            server: crate::infrastructure::config::ServerConfig {
                host: "0.0.0.0".to_string(),
                port: 8000,
            },
            database: crate::infrastructure::config::DatabaseConfig {
                url: "".to_string(),
            },
            redis: crate::infrastructure::config::RedisConfig {
                url: "".to_string(),
            },
            jwt: crate::infrastructure::config::JwtConfig {
                secret: "".to_string(),
                expiration: 3600,
                refresh_expiration: 604800,
            },
            file_storage: crate::infrastructure::config::FileStorageConfig {
                path: "".to_string(),
                max_file_size: 0,
            },
            security: security_config.clone(),
            rate_limiting: state.rate_limiting_config.clone(),
        },
    };

    router
        // Apply security headers first
        .layer(middleware::from_fn_with_state(
            security_config.clone(),
            security_headers_middleware,
        ))
        // Apply CORS
        .layer(middleware::from_fn_with_state(
            security_config.clone(),
            cors_middleware,
        ))
        // Apply request validation
        .layer(middleware::from_fn_with_state(
            security_config.clone(),
            request_validation_middleware,
        ))
        // Apply IP/User-Agent blocking
        .layer(middleware::from_fn_with_state(
            security_config.clone(),
            security_blocking_middleware,
        ))
        // Apply rate limiting if enabled
        .layer(middleware::from_fn_with_state(
            state.rate_limiters.get("general").unwrap().clone(),
            rate_limit_middleware,
        ))
        // Apply audit logging
        .layer(middleware::from_fn_with_state(
            audit_state,
            audit_middleware,
        ))
}

/// Apply specific rate limiting to auth routes
pub fn apply_auth_rate_limiting(
    router: Router,
    state: &SecurityMiddlewareState,
) -> Router {
    if let Some(auth_limiter) = state.rate_limiters.get("auth") {
        router.layer(middleware::from_fn_with_state(
            auth_limiter.clone(),
            rate_limit_middleware,
        ))
    } else {
        router
    }
}

/// Apply specific rate limiting to upload routes
pub fn apply_upload_rate_limiting(
    router: Router,
    state: &SecurityMiddlewareState,
) -> Router {
    if let Some(upload_limiter) = state.rate_limiters.get("upload") {
        router.layer(middleware::from_fn_with_state(
            upload_limiter.clone(),
            rate_limit_middleware,
        ))
    } else {
        router
    }
}

/// Apply specific rate limiting to export routes
pub fn apply_export_rate_limiting(
    router: Router,
    state: &SecurityMiddlewareState,
) -> Router {
    if let Some(export_limiter) = state.rate_limiters.get("export") {
        router.layer(middleware::from_fn_with_state(
            export_limiter.clone(),
            rate_limit_middleware,
        ))
    } else {
        router
    }
}

/// Cleanup expired rate limit buckets periodically
pub async fn cleanup_rate_limiters(state: &SecurityMiddlewareState) {
    for limiter in state.rate_limiters.values() {
        limiter.cleanup_expired();
    }
}

/// Start a background task to periodically clean up rate limiters
pub fn start_rate_limiter_cleanup_task(state: SecurityMiddlewareState) {
    tokio::spawn(async move {
        let mut interval = tokio::time::interval(std::time::Duration::from_secs(300)); // 5 minutes
        
        loop {
            interval.tick().await;
            cleanup_rate_limiters(&state).await;
        }
    });
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_create_configured_rate_limiters() {
        let config = RateLimitingConfig {
            enabled: true,
            general_max_requests: 100,
            general_window_seconds: 60,
            auth_max_requests: 10,
            auth_window_seconds: 60,
            upload_max_requests: 5,
            upload_window_seconds: 60,
            export_max_requests: 20,
            export_window_seconds: 300,
            per_ip: true,
            per_user: false,
        };

        let limiters = create_configured_rate_limiters(&config);
        
        assert!(limiters.contains_key("general"));
        assert!(limiters.contains_key("auth"));
        assert!(limiters.contains_key("upload"));
        assert!(limiters.contains_key("export"));
        assert_eq!(limiters.len(), 4);
    }
}
