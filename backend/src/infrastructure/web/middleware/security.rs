use axum::{
    extract::{Request, State},
    http::{HeaderMap, HeaderName, HeaderValue, StatusCode},
    middleware::Next,
    response::Response,
};
use std::str::FromStr;
use tracing::warn;

use crate::infrastructure::config::SecurityConfig;

/// Middleware to add security headers to all responses
pub async fn security_headers_middleware(
    State(config): State<SecurityConfig>,
    request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    let mut response = next.run(request).await;

    let headers = response.headers_mut();

    // Add security headers
    add_security_headers(headers, &config);

    Ok(response)
}

/// Middleware to block requests from banned IPs and user agents
pub async fn security_blocking_middleware(
    State(config): State<SecurityConfig>,
    headers: HeaderMap,
    request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    // Check blocked IPs
    let ip_address = extract_ip_address(&headers);
    if config.blocked_ips.contains(&ip_address) {
        warn!("Blocked request from banned IP: {}", ip_address);
        return Err(StatusCode::FORBIDDEN);
    }

    // Check blocked user agents
    if let Some(user_agent) = headers.get("user-agent") {
        if let Ok(user_agent_str) = user_agent.to_str() {
            for blocked_agent in &config.blocked_user_agents {
                if user_agent_str.to_lowercase().contains(&blocked_agent.to_lowercase()) {
                    warn!("Blocked request from banned user agent: {}", user_agent_str);
                    return Err(StatusCode::FORBIDDEN);
                }
            }
        }
    }

    Ok(next.run(request).await)
}

/// Add comprehensive security headers
fn add_security_headers(headers: &mut HeaderMap, config: &SecurityConfig) {
    // Content Security Policy - Use configured policy
    if config.enable_csp {
        if let Ok(csp) = HeaderValue::from_str(&config.csp_policy) {
            headers.insert(
                HeaderName::from_str("Content-Security-Policy").unwrap(),
                csp,
            );
        }
    }

    // HTTP Strict Transport Security - Force HTTPS
    if config.enable_hsts {
        let hsts_value = format!("max-age={}; includeSubDomains; preload", config.hsts_max_age);
        if let Ok(hsts) = HeaderValue::from_str(&hsts_value) {
            headers.insert(
                HeaderName::from_str("Strict-Transport-Security").unwrap(),
                hsts,
            );
        }
    }

    // X-Content-Type-Options - Prevent MIME sniffing
    if let Ok(content_type) = HeaderValue::from_str("nosniff") {
        headers.insert(
            HeaderName::from_str("X-Content-Type-Options").unwrap(),
            content_type,
        );
    }

    // X-Frame-Options - Prevent clickjacking
    if let Ok(frame_options) = HeaderValue::from_str("DENY") {
        headers.insert(
            HeaderName::from_str("X-Frame-Options").unwrap(),
            frame_options,
        );
    }

    // X-XSS-Protection - Enable XSS filtering
    if let Ok(xss_protection) = HeaderValue::from_str("1; mode=block") {
        headers.insert(
            HeaderName::from_str("X-XSS-Protection").unwrap(),
            xss_protection,
        );
    }

    // Referrer-Policy - Control referrer information
    if let Ok(referrer_policy) = HeaderValue::from_str("strict-origin-when-cross-origin") {
        headers.insert(
            HeaderName::from_str("Referrer-Policy").unwrap(),
            referrer_policy,
        );
    }

    // Permissions-Policy - Control browser features
    let permissions_policy = "accelerometer=(), \
                             camera=(), \
                             geolocation=(), \
                             gyroscope=(), \
                             magnetometer=(), \
                             microphone=(), \
                             payment=(), \
                             usb=()";
    
    if let Ok(permissions) = HeaderValue::from_str(permissions_policy) {
        headers.insert(
            HeaderName::from_str("Permissions-Policy").unwrap(),
            permissions,
        );
    }

    // X-Permitted-Cross-Domain-Policies - Control cross-domain policies
    if let Ok(cross_domain) = HeaderValue::from_str("none") {
        headers.insert(
            HeaderName::from_str("X-Permitted-Cross-Domain-Policies").unwrap(),
            cross_domain,
        );
    }

    // Cache-Control for API responses
    if let Ok(cache_control) = HeaderValue::from_str("no-store, no-cache, must-revalidate, private") {
        headers.insert(
            HeaderName::from_str("Cache-Control").unwrap(),
            cache_control,
        );
    }

    // Pragma for HTTP/1.0 compatibility
    if let Ok(pragma) = HeaderValue::from_str("no-cache") {
        headers.insert(
            HeaderName::from_str("Pragma").unwrap(),
            pragma,
        );
    }

    // Server header - Hide server information
    if let Ok(server) = HeaderValue::from_str("FORMS-API") {
        headers.insert(
            HeaderName::from_str("Server").unwrap(),
            server,
        );
    }
}

/// Middleware to validate request headers and block suspicious requests
pub async fn request_validation_middleware(
    State(config): State<SecurityConfig>,
    request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    if !config.enable_request_validation {
        return Ok(next.run(request).await);
    }

    let headers = request.headers();

    // Check for suspicious headers or values
    if let Err(status) = validate_request_headers(headers) {
        warn!("Blocked suspicious request: {:?}", headers);
        return Err(status);
    }

    // Check request size limits
    if let Some(content_length) = headers.get("content-length") {
        if let Ok(length_str) = content_length.to_str() {
            if let Ok(length) = length_str.parse::<u64>() {
                if length > config.max_request_size {
                    warn!("Blocked request with excessive content length: {} (max: {})", length, config.max_request_size);
                    return Err(StatusCode::PAYLOAD_TOO_LARGE);
                }
            }
        }
    }

    Ok(next.run(request).await)
}

/// Validate request headers for security issues
fn validate_request_headers(headers: &HeaderMap) -> Result<(), StatusCode> {
    // Check for common attack patterns in headers
    let suspicious_patterns = [
        "<script",
        "javascript:",
        "vbscript:",
        "onload=",
        "onerror=",
        "eval(",
        "alert(",
        "document.cookie",
        "document.write",
        "../",
        "..\\",
        "union select",
        "drop table",
        "insert into",
        "delete from",
        "update set",
    ];

    for (name, value) in headers.iter() {
        if let Ok(value_str) = value.to_str() {
            let value_lower = value_str.to_lowercase();
            
            // Check for suspicious patterns
            for pattern in &suspicious_patterns {
                if value_lower.contains(pattern) {
                    warn!("Suspicious pattern '{}' found in header '{}': {}", pattern, name, value_str);
                    return Err(StatusCode::BAD_REQUEST);
                }
            }
            
            // Check for excessively long header values
            if value_str.len() > 8192 {
                warn!("Excessively long header value in '{}': {} characters", name, value_str.len());
                return Err(StatusCode::BAD_REQUEST);
            }
        }
    }

    // Check for too many headers
    if headers.len() > 50 {
        warn!("Too many headers in request: {}", headers.len());
        return Err(StatusCode::BAD_REQUEST);
    }

    Ok(())
}

/// Middleware to add CORS headers with strict configuration
pub async fn cors_middleware(
    State(config): State<SecurityConfig>,
    request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    let method = request.method().clone();
    let origin = request.headers().get("origin").cloned();

    let mut response = next.run(request).await;
    let headers = response.headers_mut();

    // Add CORS headers
    add_cors_headers(headers, &origin, &config);

    // Handle preflight requests
    if method == axum::http::Method::OPTIONS {
        *response.status_mut() = StatusCode::NO_CONTENT;
    }

    Ok(response)
}

/// Extract IP address from headers, considering proxy headers
fn extract_ip_address(headers: &HeaderMap) -> String {
    // Check for common proxy headers in order of preference
    let ip_headers = [
        "x-forwarded-for",
        "x-real-ip",
        "cf-connecting-ip", // Cloudflare
        "x-client-ip",
        "x-forwarded",
        "forwarded-for",
        "forwarded",
    ];

    for header_name in &ip_headers {
        if let Some(header_value) = headers.get(*header_name) {
            if let Ok(value) = header_value.to_str() {
                // X-Forwarded-For can contain multiple IPs, take the first one
                let ip = value.split(',').next().unwrap_or(value).trim();
                if !ip.is_empty() && ip != "unknown" {
                    return ip.to_string();
                }
            }
        }
    }

    "unknown".to_string()
}

/// Add CORS headers with security considerations
fn add_cors_headers(headers: &mut HeaderMap, origin: &Option<HeaderValue>, config: &SecurityConfig) {
    // Check if origin is allowed
    let origin_allowed = if let Some(origin_value) = origin {
        if let Ok(origin_str) = origin_value.to_str() {
            config.cors_allowed_origins.contains(&origin_str.to_string())
        } else {
            false
        }
    } else {
        false
    };

    if origin_allowed {
        if let Some(origin_value) = origin {
            headers.insert(
                HeaderName::from_str("Access-Control-Allow-Origin").unwrap(),
                origin_value.clone(),
            );
        }
    } else {
        // Default to deny
        if let Ok(deny) = HeaderValue::from_str("null") {
            headers.insert(
                HeaderName::from_str("Access-Control-Allow-Origin").unwrap(),
                deny,
            );
        }
    }

    // Allowed methods
    let methods_str = config.cors_allowed_methods.join(", ");
    if let Ok(methods) = HeaderValue::from_str(&methods_str) {
        headers.insert(
            HeaderName::from_str("Access-Control-Allow-Methods").unwrap(),
            methods,
        );
    }

    // Allowed headers
    let headers_str = config.cors_allowed_headers.join(", ");
    if let Ok(allowed_headers) = HeaderValue::from_str(&headers_str) {
        headers.insert(
            HeaderName::from_str("Access-Control-Allow-Headers").unwrap(),
            allowed_headers,
        );
    }

    // Exposed headers
    if let Ok(exposed_headers) = HeaderValue::from_str("X-Request-ID, X-Total-Count") {
        headers.insert(
            HeaderName::from_str("Access-Control-Expose-Headers").unwrap(),
            exposed_headers,
        );
    }

    // Max age for preflight cache
    if let Ok(max_age) = HeaderValue::from_str(&config.cors_max_age.to_string()) {
        headers.insert(
            HeaderName::from_str("Access-Control-Max-Age").unwrap(),
            max_age,
        );
    }

    // Allow credentials
    if let Ok(credentials) = HeaderValue::from_str("true") {
        headers.insert(
            HeaderName::from_str("Access-Control-Allow-Credentials").unwrap(),
            credentials,
        );
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use axum::http::HeaderValue;

    #[test]
    fn test_validate_request_headers_normal() {
        let mut headers = HeaderMap::new();
        headers.insert("content-type", HeaderValue::from_static("application/json"));
        headers.insert("authorization", HeaderValue::from_static("Bearer token123"));
        
        assert!(validate_request_headers(&headers).is_ok());
    }

    #[test]
    fn test_validate_request_headers_suspicious() {
        let mut headers = HeaderMap::new();
        headers.insert("x-custom", HeaderValue::from_static("<script>alert('xss')</script>"));
        
        assert!(validate_request_headers(&headers).is_err());
    }

    #[test]
    fn test_validate_request_headers_too_long() {
        let mut headers = HeaderMap::new();
        let long_value = "a".repeat(10000);
        headers.insert("x-custom", HeaderValue::from_str(&long_value).unwrap());
        
        assert!(validate_request_headers(&headers).is_err());
    }
}
