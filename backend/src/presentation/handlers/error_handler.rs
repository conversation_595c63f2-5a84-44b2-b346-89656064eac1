use std::sync::Arc;
use axum::{
    extract::{Query, State},
    response::<PERSON><PERSON>,
    Extension,
};
use serde::{Deserialize, Serialize};

use crate::infrastructure::monitoring::{ErrorMonitoringService, ErrorStatisticsReport};
use crate::infrastructure::error_recovery::ErrorRecoveryService;
use crate::error::{AppError, AppResult};
use crate::presentation::handlers::audit_handler::CurrentUser;

/// Application state for error handlers
#[derive(Clone)]
pub struct ErrorHandlerState {
    pub error_monitor: Arc<ErrorMonitoringService>,
    pub error_recovery: Arc<ErrorRecoveryService>,
}

/// Query parameters for error statistics
#[derive(Debug, Deserialize)]
pub struct ErrorStatsQuery {
    pub reset: Option<bool>,
}

/// Error statistics response
#[derive(Debug, Serialize)]
pub struct ErrorStatsResponse {
    pub statistics: ErrorStatisticsReport,
    pub circuit_breakers: std::collections::HashMap<String, CircuitBreakerStatusResponse>,
    pub health_status: String,
    pub recommendations: Vec<String>,
}

/// Circuit breaker status response
#[derive(Debug, Serialize)]
pub struct CircuitBreakerStatusResponse {
    pub state: String,
    pub failure_count: u32,
    pub success_count: u32,
    pub last_failure_time: Option<String>,
}

/// Error trend response
#[derive(Debug, Serialize)]
pub struct ErrorTrendResponse {
    pub error_code: String,
    pub trend: String, // "increasing", "decreasing", "stable"
    pub current_rate: f64,
    pub previous_rate: f64,
    pub change_percentage: f64,
}

/// Get error statistics and monitoring data
pub async fn get_error_statistics(
    State(state): State<ErrorHandlerState>,
    Extension(current_user): Extension<CurrentUser>,
    Query(query): Query<ErrorStatsQuery>,
) -> AppResult<Json<ErrorStatsResponse>> {
    // Check permissions - only admins can view error statistics
    if !current_user.is_admin || !current_user.permissions.contains(&"errors:read".to_string()) {
        return Err(AppError::permission_denied("errors:read"));
    }

    // Reset statistics if requested
    if query.reset.unwrap_or(false) {
        state.error_monitor.reset_statistics();
    }

    // Get error statistics
    let statistics = state.error_monitor.get_error_statistics();

    // Get circuit breaker status
    let circuit_breakers = state.error_recovery
        .get_circuit_breaker_status()
        .into_iter()
        .map(|(name, status)| {
            (
                name,
                CircuitBreakerStatusResponse {
                    state: status.state,
                    failure_count: status.failure_count,
                    success_count: status.success_count,
                    last_failure_time: status.last_failure_time
                        .map(|t| format!("{:?}", t)), // Convert Instant to string
                },
            )
        })
        .collect();

    // Determine health status
    let health_status = determine_health_status(&statistics, &circuit_breakers);

    // Generate recommendations
    let recommendations = generate_error_recommendations(&statistics, &circuit_breakers);

    Ok(Json(ErrorStatsResponse {
        statistics,
        circuit_breakers,
        health_status,
        recommendations,
    }))
}

/// Get error trends and analysis
pub async fn get_error_trends(
    State(state): State<ErrorHandlerState>,
    Extension(current_user): Extension<CurrentUser>,
) -> AppResult<Json<Vec<ErrorTrendResponse>>> {
    // Check permissions
    if !current_user.is_admin || !current_user.permissions.contains(&"errors:read".to_string()) {
        return Err(AppError::permission_denied("errors:read"));
    }

    // For now, return empty trends as we'd need historical data
    // In a real implementation, this would analyze error trends over time
    let trends = vec![];

    Ok(Json(trends))
}

/// Reset error statistics
pub async fn reset_error_statistics(
    State(state): State<ErrorHandlerState>,
    Extension(current_user): Extension<CurrentUser>,
) -> AppResult<Json<serde_json::Value>> {
    // Check permissions - only admins can reset statistics
    if !current_user.is_admin || !current_user.permissions.contains(&"errors:write".to_string()) {
        return Err(AppError::permission_denied("errors:write"));
    }

    state.error_monitor.reset_statistics();

    Ok(Json(serde_json::json!({
        "success": true,
        "message": "Error statistics reset successfully",
        "timestamp": chrono::Utc::now().to_rfc3339()
    })))
}

/// Test error handling (for development/testing)
pub async fn test_error_handling(
    State(state): State<ErrorHandlerState>,
    Extension(current_user): Extension<CurrentUser>,
    Query(error_type): Query<TestErrorQuery>,
) -> AppResult<Json<serde_json::Value>> {
    // Check permissions - only admins can test error handling
    if !current_user.is_admin || !current_user.permissions.contains(&"errors:test".to_string()) {
        return Err(AppError::permission_denied("errors:test"));
    }

    // Only allow in development environment
    if std::env::var("RUST_ENV").unwrap_or_default() == "production" {
        return Err(AppError::authorization("Error testing not allowed in production"));
    }

    // Generate test error based on type
    let test_error = match error_type.error_type.as_deref() {
        Some("validation") => AppError::validation("Test validation error"),
        Some("authentication") => AppError::authentication("Test authentication error"),
        Some("authorization") => AppError::authorization("Test authorization error"),
        Some("not_found") => AppError::not_found("test_resource"),
        Some("conflict") => AppError::conflict("Test conflict error"),
        Some("rate_limit") => AppError::RateLimitExceeded,
        Some("timeout") => AppError::timeout("test_operation"),
        Some("external_service") => AppError::external_service("test_service", "Test external service error"),
        Some("internal") => AppError::internal("Test internal error"),
        _ => AppError::bad_request("Unknown test error type"),
    };

    // Record the test error
    let mut context = std::collections::HashMap::new();
    context.insert("test_error".to_string(), "true".to_string());
    context.insert("user_id".to_string(), current_user.user_id.to_string());
    
    state.error_monitor.record_error(&test_error, Some(context));

    // Return the error (this will trigger error handling middleware)
    Err(test_error)
}

#[derive(Debug, Deserialize)]
pub struct TestErrorQuery {
    pub error_type: Option<String>,
}

/// Determine overall health status based on error statistics
fn determine_health_status(
    statistics: &ErrorStatisticsReport,
    circuit_breakers: &std::collections::HashMap<String, CircuitBreakerStatusResponse>,
) -> String {
    // Check for critical issues
    if statistics.overall_error_rate > 50.0 {
        return "critical".to_string();
    }

    // Check for open circuit breakers
    let open_breakers = circuit_breakers
        .values()
        .filter(|cb| cb.state == "Open")
        .count();

    if open_breakers > 0 {
        return "degraded".to_string();
    }

    // Check for high error rate
    if statistics.overall_error_rate > 10.0 {
        return "warning".to_string();
    }

    // Check for any errors in the last period
    if statistics.total_errors > 0 && statistics.overall_error_rate > 1.0 {
        return "warning".to_string();
    }

    "healthy".to_string()
}

/// Generate recommendations based on error patterns
fn generate_error_recommendations(
    statistics: &ErrorStatisticsReport,
    circuit_breakers: &std::collections::HashMap<String, CircuitBreakerStatusResponse>,
) -> Vec<String> {
    let mut recommendations = Vec::new();

    // High error rate recommendations
    if statistics.overall_error_rate > 10.0 {
        recommendations.push("High error rate detected. Review recent changes and monitor system resources.".to_string());
    }

    // Circuit breaker recommendations
    let open_breakers: Vec<_> = circuit_breakers
        .iter()
        .filter(|(_, cb)| cb.state == "Open")
        .map(|(name, _)| name.clone())
        .collect();

    if !open_breakers.is_empty() {
        recommendations.push(format!(
            "Circuit breakers are open for: {}. Check external service health.",
            open_breakers.join(", ")
        ));
    }

    // Error pattern recommendations
    let high_frequency_errors: Vec<_> = statistics.error_details
        .iter()
        .filter(|detail| detail.error_rate > 5.0)
        .collect();

    if !high_frequency_errors.is_empty() {
        recommendations.push("High frequency errors detected. Consider implementing additional validation or rate limiting.".to_string());
    }

    // Database error recommendations
    let has_db_errors = statistics.error_details
        .iter()
        .any(|detail| detail.error_code.contains("DATABASE"));

    if has_db_errors {
        recommendations.push("Database errors detected. Check database connectivity and performance.".to_string());
    }

    // Authentication error recommendations
    let auth_errors: Vec<_> = statistics.error_details
        .iter()
        .filter(|detail| detail.error_code.contains("AUTHENTICATION") || detail.error_code.contains("AUTHORIZATION"))
        .collect();

    if !auth_errors.is_empty() {
        recommendations.push("Authentication/authorization errors detected. Review access patterns and security policies.".to_string());
    }

    // If no specific recommendations, provide general advice
    if recommendations.is_empty() && statistics.total_errors > 0 {
        recommendations.push("Monitor error trends and consider implementing additional logging for better diagnostics.".to_string());
    }

    recommendations
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::infrastructure::monitoring::ErrorDetail;

    #[test]
    fn test_determine_health_status() {
        let mut circuit_breakers = std::collections::HashMap::new();
        
        // Healthy system
        let stats = ErrorStatisticsReport {
            total_errors: 0,
            uptime_seconds: 3600,
            overall_error_rate: 0.0,
            error_details: vec![],
            monitoring_window_seconds: 300,
        };
        assert_eq!(determine_health_status(&stats, &circuit_breakers), "healthy");

        // High error rate
        let stats = ErrorStatisticsReport {
            total_errors: 100,
            uptime_seconds: 60,
            overall_error_rate: 60.0,
            error_details: vec![],
            monitoring_window_seconds: 300,
        };
        assert_eq!(determine_health_status(&stats, &circuit_breakers), "critical");

        // Open circuit breaker
        circuit_breakers.insert("test_service".to_string(), CircuitBreakerStatusResponse {
            state: "Open".to_string(),
            failure_count: 5,
            success_count: 0,
            last_failure_time: None,
        });
        
        let stats = ErrorStatisticsReport {
            total_errors: 5,
            uptime_seconds: 300,
            overall_error_rate: 1.0,
            error_details: vec![],
            monitoring_window_seconds: 300,
        };
        assert_eq!(determine_health_status(&stats, &circuit_breakers), "degraded");
    }

    #[test]
    fn test_generate_error_recommendations() {
        let circuit_breakers = std::collections::HashMap::new();
        
        let stats = ErrorStatisticsReport {
            total_errors: 50,
            uptime_seconds: 300,
            overall_error_rate: 15.0,
            error_details: vec![
                ErrorDetail {
                    error_code: "DATABASE_ERROR".to_string(),
                    count: 30,
                    error_rate: 6.0,
                    first_occurrence: std::time::Instant::now(),
                    last_occurrence: std::time::Instant::now(),
                },
                ErrorDetail {
                    error_code: "AUTHENTICATION_ERROR".to_string(),
                    count: 20,
                    error_rate: 4.0,
                    first_occurrence: std::time::Instant::now(),
                    last_occurrence: std::time::Instant::now(),
                },
            ],
            monitoring_window_seconds: 300,
        };

        let recommendations = generate_error_recommendations(&stats, &circuit_breakers);
        
        assert!(!recommendations.is_empty());
        assert!(recommendations.iter().any(|r| r.contains("High error rate")));
        assert!(recommendations.iter().any(|r| r.contains("Database errors")));
        assert!(recommendations.iter().any(|r| r.contains("Authentication")));
    }
}
