use axum::{
    extract::State,
    http::StatusCode,
    response::J<PERSON>,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use std::time::Instant;
use sqlx::PgPool;
use redis::aio::ConnectionManager;

/// Health check response
#[derive(Debug, Serialize)]
pub struct HealthResponse {
    pub status: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub service: String,
    pub version: String,
    pub uptime_seconds: u64,
    pub checks: HealthChecks,
}

/// Individual health checks
#[derive(Debug, Serialize)]
pub struct HealthChecks {
    pub database: HealthCheckResult,
    pub redis: HealthCheckResult,
    pub memory: HealthCheckResult,
    pub disk: HealthCheckResult,
}

/// Individual health check result
#[derive(Debug, Serialize)]
pub struct HealthCheckResult {
    pub status: String,
    pub response_time_ms: Option<u64>,
    pub message: Option<String>,
    pub details: Option<serde_json::Value>,
}

/// Application state for health checks
#[derive(Clone)]
pub struct HealthState {
    pub db_pool: PgPool,
    pub redis_manager: ConnectionManager,
    pub start_time: Instant,
}

/// Basic health check endpoint
pub async fn health_check() -> Result<Json<serde_json::Value>, StatusCode> {
    Ok(Json(serde_json::json!({
        "status": "healthy",
        "timestamp": chrono::Utc::now().to_rfc3339(),
        "service": "forms-backend",
        "version": env!("CARGO_PKG_VERSION")
    })))
}

/// Comprehensive health check with dependencies
pub async fn detailed_health_check(
    State(state): State<HealthState>,
) -> Result<Json<HealthResponse>, StatusCode> {
    let start_time = Instant::now();
    
    // Run all health checks concurrently
    let (db_check, redis_check, memory_check, disk_check) = tokio::join!(
        check_database(&state.db_pool),
        check_redis(&state.redis_manager),
        check_memory(),
        check_disk()
    );

    let uptime = state.start_time.elapsed().as_secs();
    
    // Determine overall status
    let overall_status = if db_check.status == "healthy" 
        && redis_check.status == "healthy" 
        && memory_check.status == "healthy" 
        && disk_check.status == "healthy" {
        "healthy"
    } else if db_check.status == "unhealthy" || redis_check.status == "unhealthy" {
        "unhealthy"
    } else {
        "degraded"
    };

    let response = HealthResponse {
        status: overall_status.to_string(),
        timestamp: chrono::Utc::now(),
        service: "forms-backend".to_string(),
        version: env!("CARGO_PKG_VERSION").to_string(),
        uptime_seconds: uptime,
        checks: HealthChecks {
            database: db_check,
            redis: redis_check,
            memory: memory_check,
            disk: disk_check,
        },
    };

    // Return appropriate status code
    let status_code = match overall_status {
        "healthy" => StatusCode::OK,
        "degraded" => StatusCode::OK, // Still operational
        _ => StatusCode::SERVICE_UNAVAILABLE,
    };

    match status_code {
        StatusCode::OK => Ok(Json(response)),
        _ => Err(status_code),
    }
}

/// Check database connectivity
async fn check_database(pool: &PgPool) -> HealthCheckResult {
    let start = Instant::now();
    
    match sqlx::query_scalar::<_, i32>("SELECT 1")
        .fetch_one(pool)
        .await
    {
        Ok(result) if result == 1 => {
            let response_time = start.elapsed().as_millis() as u64;
            HealthCheckResult {
                status: "healthy".to_string(),
                response_time_ms: Some(response_time),
                message: Some("Database connection successful".to_string()),
                details: Some(serde_json::json!({
                    "pool_size": pool.size(),
                    "idle_connections": pool.num_idle(),
                })),
            }
        }
        Ok(_) => HealthCheckResult {
            status: "unhealthy".to_string(),
            response_time_ms: Some(start.elapsed().as_millis() as u64),
            message: Some("Database returned unexpected result".to_string()),
            details: None,
        },
        Err(e) => HealthCheckResult {
            status: "unhealthy".to_string(),
            response_time_ms: Some(start.elapsed().as_millis() as u64),
            message: Some(format!("Database connection failed: {}", e)),
            details: None,
        },
    }
}

/// Check Redis connectivity
async fn check_redis(manager: &ConnectionManager) -> HealthCheckResult {
    let start = Instant::now();
    
    match redis::cmd("PING")
        .query_async::<_, String>(&mut manager.clone())
        .await
    {
        Ok(response) if response == "PONG" => {
            let response_time = start.elapsed().as_millis() as u64;
            HealthCheckResult {
                status: "healthy".to_string(),
                response_time_ms: Some(response_time),
                message: Some("Redis connection successful".to_string()),
                details: None,
            }
        }
        Ok(_) => HealthCheckResult {
            status: "unhealthy".to_string(),
            response_time_ms: Some(start.elapsed().as_millis() as u64),
            message: Some("Redis returned unexpected response".to_string()),
            details: None,
        },
        Err(e) => HealthCheckResult {
            status: "unhealthy".to_string(),
            response_time_ms: Some(start.elapsed().as_millis() as u64),
            message: Some(format!("Redis connection failed: {}", e)),
            details: None,
        },
    }
}

/// Check memory usage
async fn check_memory() -> HealthCheckResult {
    // This is a simplified memory check
    // In production, you might want to use a more sophisticated approach
    
    #[cfg(target_os = "linux")]
    {
        match std::fs::read_to_string("/proc/meminfo") {
            Ok(content) => {
                let mut total_kb = 0u64;
                let mut available_kb = 0u64;
                
                for line in content.lines() {
                    if line.starts_with("MemTotal:") {
                        if let Some(value) = line.split_whitespace().nth(1) {
                            total_kb = value.parse().unwrap_or(0);
                        }
                    } else if line.starts_with("MemAvailable:") {
                        if let Some(value) = line.split_whitespace().nth(1) {
                            available_kb = value.parse().unwrap_or(0);
                        }
                    }
                }
                
                if total_kb > 0 {
                    let used_percent = ((total_kb - available_kb) as f64 / total_kb as f64) * 100.0;
                    let status = if used_percent > 90.0 { "unhealthy" } else if used_percent > 80.0 { "warning" } else { "healthy" };
                    
                    HealthCheckResult {
                        status: status.to_string(),
                        response_time_ms: None,
                        message: Some(format!("Memory usage: {:.1}%", used_percent)),
                        details: Some(serde_json::json!({
                            "total_kb": total_kb,
                            "available_kb": available_kb,
                            "used_percent": used_percent,
                        })),
                    }
                } else {
                    HealthCheckResult {
                        status: "unknown".to_string(),
                        response_time_ms: None,
                        message: Some("Could not parse memory information".to_string()),
                        details: None,
                    }
                }
            }
            Err(e) => HealthCheckResult {
                status: "unknown".to_string(),
                response_time_ms: None,
                message: Some(format!("Could not read memory information: {}", e)),
                details: None,
            },
        }
    }
    
    #[cfg(not(target_os = "linux"))]
    {
        HealthCheckResult {
            status: "unknown".to_string(),
            response_time_ms: None,
            message: Some("Memory check not implemented for this platform".to_string()),
            details: None,
        }
    }
}

/// Check disk usage
async fn check_disk() -> HealthCheckResult {
    // This is a simplified disk check
    // In production, you might want to check specific mount points
    
    #[cfg(unix)]
    {
        use std::ffi::CString;
        use std::mem;
        
        extern "C" {
            fn statvfs(path: *const libc::c_char, buf: *mut libc::statvfs) -> libc::c_int;
        }
        
        let path = CString::new("/").unwrap();
        let mut stat: libc::statvfs = unsafe { mem::zeroed() };
        
        let result = unsafe { statvfs(path.as_ptr(), &mut stat) };
        
        if result == 0 {
            let total_bytes = stat.f_blocks as u64 * stat.f_frsize;
            let available_bytes = stat.f_bavail as u64 * stat.f_frsize;
            let used_bytes = total_bytes - available_bytes;
            let used_percent = (used_bytes as f64 / total_bytes as f64) * 100.0;
            
            let status = if used_percent > 95.0 { "unhealthy" } else if used_percent > 85.0 { "warning" } else { "healthy" };
            
            HealthCheckResult {
                status: status.to_string(),
                response_time_ms: None,
                message: Some(format!("Disk usage: {:.1}%", used_percent)),
                details: Some(serde_json::json!({
                    "total_bytes": total_bytes,
                    "available_bytes": available_bytes,
                    "used_percent": used_percent,
                })),
            }
        } else {
            HealthCheckResult {
                status: "unknown".to_string(),
                response_time_ms: None,
                message: Some("Could not get disk information".to_string()),
                details: None,
            }
        }
    }
    
    #[cfg(not(unix))]
    {
        HealthCheckResult {
            status: "unknown".to_string(),
            response_time_ms: None,
            message: Some("Disk check not implemented for this platform".to_string()),
            details: None,
        }
    }
}

/// Readiness check - simpler check for load balancers
pub async fn readiness_check(
    State(state): State<HealthState>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    // Quick database check
    match sqlx::query_scalar::<_, i32>("SELECT 1")
        .fetch_one(&state.db_pool)
        .await
    {
        Ok(1) => Ok(Json(serde_json::json!({
            "status": "ready",
            "timestamp": chrono::Utc::now().to_rfc3339()
        }))),
        _ => Err(StatusCode::SERVICE_UNAVAILABLE),
    }
}

/// Liveness check - very basic check
pub async fn liveness_check() -> Result<Json<serde_json::Value>, StatusCode> {
    Ok(Json(serde_json::json!({
        "status": "alive",
        "timestamp": chrono::Utc::now().to_rfc3339()
    })))
}
