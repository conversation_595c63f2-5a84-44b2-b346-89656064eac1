{"$message_type":"diagnostic","message":"unused imports: `CreateUserRequest` and `UpdateUserRequest`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/domain/repositories/user_repository.rs","byte_start":83,"byte_end":100,"line_start":4,"line_end":4,"column_start":37,"column_end":54,"is_primary":true,"text":[{"text":"use crate::domain::entities::{User, CreateUserRequest, UpdateUserRequest};","highlight_start":37,"highlight_end":54}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/domain/repositories/user_repository.rs","byte_start":102,"byte_end":119,"line_start":4,"line_end":4,"column_start":56,"column_end":73,"is_primary":true,"text":[{"text":"use crate::domain::entities::{User, CreateUserRequest, UpdateUserRequest};","highlight_start":56,"highlight_end":73}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/domain/repositories/user_repository.rs","byte_start":81,"byte_end":119,"line_start":4,"line_end":4,"column_start":35,"column_end":73,"is_primary":true,"text":[{"text":"use crate::domain::entities::{User, CreateUserRequest, UpdateUserRequest};","highlight_start":35,"highlight_end":73}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/domain/repositories/user_repository.rs","byte_start":76,"byte_end":77,"line_start":4,"line_end":4,"column_start":30,"column_end":31,"is_primary":true,"text":[{"text":"use crate::domain::entities::{User, CreateUserRequest, UpdateUserRequest};","highlight_start":30,"highlight_end":31}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/domain/repositories/user_repository.rs","byte_start":119,"byte_end":120,"line_start":4,"line_end":4,"column_start":73,"column_end":74,"is_primary":true,"text":[{"text":"use crate::domain::entities::{User, CreateUserRequest, UpdateUserRequest};","highlight_start":73,"highlight_end":74}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `CreateUserRequest` and `UpdateUserRequest`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domain/repositories/user_repository.rs:4:37\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::domain::entities::{User, CreateUserRequest, UpdateUserRequest};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `CreateTenantRequest` and `UpdateTenantRequest`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/domain/repositories/tenant_repository.rs","byte_start":85,"byte_end":104,"line_start":4,"line_end":4,"column_start":39,"column_end":58,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Tenant, CreateTenantRequest, UpdateTenantRequest};","highlight_start":39,"highlight_end":58}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/domain/repositories/tenant_repository.rs","byte_start":106,"byte_end":125,"line_start":4,"line_end":4,"column_start":60,"column_end":79,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Tenant, CreateTenantRequest, UpdateTenantRequest};","highlight_start":60,"highlight_end":79}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/domain/repositories/tenant_repository.rs","byte_start":83,"byte_end":125,"line_start":4,"line_end":4,"column_start":37,"column_end":79,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Tenant, CreateTenantRequest, UpdateTenantRequest};","highlight_start":37,"highlight_end":79}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/domain/repositories/tenant_repository.rs","byte_start":76,"byte_end":77,"line_start":4,"line_end":4,"column_start":30,"column_end":31,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Tenant, CreateTenantRequest, UpdateTenantRequest};","highlight_start":30,"highlight_end":31}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/domain/repositories/tenant_repository.rs","byte_start":125,"byte_end":126,"line_start":4,"line_end":4,"column_start":79,"column_end":80,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Tenant, CreateTenantRequest, UpdateTenantRequest};","highlight_start":79,"highlight_end":80}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `CreateTenantRequest` and `UpdateTenantRequest`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domain/repositories/tenant_repository.rs:4:39\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::domain::entities::{Tenant, CreateTenantRequest, UpdateTenantRequest};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `CreateEmployeeRequest` and `UpdateEmployeeRequest`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/domain/repositories/employee_repository.rs","byte_start":87,"byte_end":108,"line_start":4,"line_end":4,"column_start":41,"column_end":62,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Employee, CreateEmployeeRequest, UpdateEmployeeRequest};","highlight_start":41,"highlight_end":62}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/domain/repositories/employee_repository.rs","byte_start":110,"byte_end":131,"line_start":4,"line_end":4,"column_start":64,"column_end":85,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Employee, CreateEmployeeRequest, UpdateEmployeeRequest};","highlight_start":64,"highlight_end":85}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/domain/repositories/employee_repository.rs","byte_start":85,"byte_end":131,"line_start":4,"line_end":4,"column_start":39,"column_end":85,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Employee, CreateEmployeeRequest, UpdateEmployeeRequest};","highlight_start":39,"highlight_end":85}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/domain/repositories/employee_repository.rs","byte_start":76,"byte_end":77,"line_start":4,"line_end":4,"column_start":30,"column_end":31,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Employee, CreateEmployeeRequest, UpdateEmployeeRequest};","highlight_start":30,"highlight_end":31}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/domain/repositories/employee_repository.rs","byte_start":131,"byte_end":132,"line_start":4,"line_end":4,"column_start":85,"column_end":86,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Employee, CreateEmployeeRequest, UpdateEmployeeRequest};","highlight_start":85,"highlight_end":86}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `CreateEmployeeRequest` and `UpdateEmployeeRequest`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domain/repositories/employee_repository.rs:4:41\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0mties::{Employee, CreateEmployeeRequest, UpdateEmployeeRequest};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `CreateCompanyRequest` and `UpdateCompanyRequest`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/domain/repositories/company_repository.rs","byte_start":86,"byte_end":106,"line_start":4,"line_end":4,"column_start":40,"column_end":60,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Company, CreateCompanyRequest, UpdateCompanyRequest};","highlight_start":40,"highlight_end":60}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/domain/repositories/company_repository.rs","byte_start":108,"byte_end":128,"line_start":4,"line_end":4,"column_start":62,"column_end":82,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Company, CreateCompanyRequest, UpdateCompanyRequest};","highlight_start":62,"highlight_end":82}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/domain/repositories/company_repository.rs","byte_start":84,"byte_end":128,"line_start":4,"line_end":4,"column_start":38,"column_end":82,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Company, CreateCompanyRequest, UpdateCompanyRequest};","highlight_start":38,"highlight_end":82}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/domain/repositories/company_repository.rs","byte_start":76,"byte_end":77,"line_start":4,"line_end":4,"column_start":30,"column_end":31,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Company, CreateCompanyRequest, UpdateCompanyRequest};","highlight_start":30,"highlight_end":31}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/domain/repositories/company_repository.rs","byte_start":128,"byte_end":129,"line_start":4,"line_end":4,"column_start":82,"column_end":83,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Company, CreateCompanyRequest, UpdateCompanyRequest};","highlight_start":82,"highlight_end":83}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `CreateCompanyRequest` and `UpdateCompanyRequest`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domain/repositories/company_repository.rs:4:40\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::domain::entities::{Company, CreateCompanyRequest, UpdateCompanyRequest};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `CreateContactRequest` and `UpdateContactRequest`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/domain/repositories/contact_repository.rs","byte_start":86,"byte_end":106,"line_start":4,"line_end":4,"column_start":40,"column_end":60,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Contact, CreateContactRequest, UpdateContactRequest};","highlight_start":40,"highlight_end":60}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/domain/repositories/contact_repository.rs","byte_start":108,"byte_end":128,"line_start":4,"line_end":4,"column_start":62,"column_end":82,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Contact, CreateContactRequest, UpdateContactRequest};","highlight_start":62,"highlight_end":82}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/domain/repositories/contact_repository.rs","byte_start":84,"byte_end":128,"line_start":4,"line_end":4,"column_start":38,"column_end":82,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Contact, CreateContactRequest, UpdateContactRequest};","highlight_start":38,"highlight_end":82}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/domain/repositories/contact_repository.rs","byte_start":76,"byte_end":77,"line_start":4,"line_end":4,"column_start":30,"column_end":31,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Contact, CreateContactRequest, UpdateContactRequest};","highlight_start":30,"highlight_end":31}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/domain/repositories/contact_repository.rs","byte_start":128,"byte_end":129,"line_start":4,"line_end":4,"column_start":82,"column_end":83,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Contact, CreateContactRequest, UpdateContactRequest};","highlight_start":82,"highlight_end":83}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `CreateContactRequest` and `UpdateContactRequest`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domain/repositories/contact_repository.rs:4:40\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::domain::entities::{Contact, CreateContactRequest, UpdateContactRequest};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `CreateFormRequest`, `FormItem`, and `UpdateFormRequest`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/domain/repositories/form_repository.rs","byte_start":83,"byte_end":100,"line_start":4,"line_end":4,"column_start":37,"column_end":54,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Form, CreateFormRequest, UpdateFormRequest, FormItem};","highlight_start":37,"highlight_end":54}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/domain/repositories/form_repository.rs","byte_start":102,"byte_end":119,"line_start":4,"line_end":4,"column_start":56,"column_end":73,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Form, CreateFormRequest, UpdateFormRequest, FormItem};","highlight_start":56,"highlight_end":73}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/domain/repositories/form_repository.rs","byte_start":121,"byte_end":129,"line_start":4,"line_end":4,"column_start":75,"column_end":83,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Form, CreateFormRequest, UpdateFormRequest, FormItem};","highlight_start":75,"highlight_end":83}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/domain/repositories/form_repository.rs","byte_start":81,"byte_end":129,"line_start":4,"line_end":4,"column_start":35,"column_end":83,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Form, CreateFormRequest, UpdateFormRequest, FormItem};","highlight_start":35,"highlight_end":83}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/domain/repositories/form_repository.rs","byte_start":76,"byte_end":77,"line_start":4,"line_end":4,"column_start":30,"column_end":31,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Form, CreateFormRequest, UpdateFormRequest, FormItem};","highlight_start":30,"highlight_end":31}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/domain/repositories/form_repository.rs","byte_start":129,"byte_end":130,"line_start":4,"line_end":4,"column_start":83,"column_end":84,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Form, CreateFormRequest, UpdateFormRequest, FormItem};","highlight_start":83,"highlight_end":84}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `CreateFormRequest`, `FormItem`, and `UpdateFormRequest`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domain/repositories/form_repository.rs:4:37\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::domain::entities::{Form, CreateFormRequest, UpdateFormRequest, FormItem};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `CreateAttachmentRequest`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/domain/repositories/attachment_repository.rs","byte_start":89,"byte_end":112,"line_start":4,"line_end":4,"column_start":43,"column_end":66,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Attachment, CreateAttachmentRequest};","highlight_start":43,"highlight_end":66}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/domain/repositories/attachment_repository.rs","byte_start":87,"byte_end":112,"line_start":4,"line_end":4,"column_start":41,"column_end":66,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Attachment, CreateAttachmentRequest};","highlight_start":41,"highlight_end":66}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/domain/repositories/attachment_repository.rs","byte_start":76,"byte_end":77,"line_start":4,"line_end":4,"column_start":30,"column_end":31,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Attachment, CreateAttachmentRequest};","highlight_start":30,"highlight_end":31}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/domain/repositories/attachment_repository.rs","byte_start":112,"byte_end":113,"line_start":4,"line_end":4,"column_start":66,"column_end":67,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Attachment, CreateAttachmentRequest};","highlight_start":66,"highlight_end":67}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `CreateAttachmentRequest`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domain/repositories/attachment_repository.rs:4:43\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::domain::entities::{Attachment, CreateAttachmentRequest};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `PaginationRequest`, `Pagination`, and `SearchQuery`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/domain/repositories/attachment_repository.rs","byte_start":150,"byte_end":167,"line_start":5,"line_end":5,"column_start":36,"column_end":53,"is_primary":true,"text":[{"text":"use crate::domain::value_objects::{PaginationRequest, Pagination, SearchQuery};","highlight_start":36,"highlight_end":53}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/domain/repositories/attachment_repository.rs","byte_start":169,"byte_end":179,"line_start":5,"line_end":5,"column_start":55,"column_end":65,"is_primary":true,"text":[{"text":"use crate::domain::value_objects::{PaginationRequest, Pagination, SearchQuery};","highlight_start":55,"highlight_end":65}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/domain/repositories/attachment_repository.rs","byte_start":181,"byte_end":192,"line_start":5,"line_end":5,"column_start":67,"column_end":78,"is_primary":true,"text":[{"text":"use crate::domain::value_objects::{PaginationRequest, Pagination, SearchQuery};","highlight_start":67,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/domain/repositories/attachment_repository.rs","byte_start":115,"byte_end":195,"line_start":5,"line_end":6,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::domain::value_objects::{PaginationRequest, Pagination, SearchQuery};","highlight_start":1,"highlight_end":80},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `PaginationRequest`, `Pagination`, and `SearchQuery`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domain/repositories/attachment_repository.rs:5:36\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::domain::value_objects::{PaginationRequest, Pagination, SearchQuery};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `async_trait::async_trait`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/domain/services/user_service.rs","byte_start":4,"byte_end":28,"line_start":1,"line_end":1,"column_start":5,"column_end":29,"is_primary":true,"text":[{"text":"use async_trait::async_trait;","highlight_start":5,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/domain/services/user_service.rs","byte_start":0,"byte_end":30,"line_start":1,"line_end":2,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use async_trait::async_trait;","highlight_start":1,"highlight_end":30},{"text":"use uuid::Uuid;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `async_trait::async_trait`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domain/services/user_service.rs:1:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse async_trait::async_trait;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"src/domain/services/auth_service.rs","byte_start":1033,"byte_end":1041,"line_start":38,"line_end":38,"column_start":13,"column_end":21,"is_primary":true,"text":[{"text":"        let mut user = user.ok_or(\"Invalid credentials\")?;","highlight_start":13,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_mut)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"src/domain/services/auth_service.rs","byte_start":1033,"byte_end":1037,"line_start":38,"line_end":38,"column_start":13,"column_end":17,"is_primary":true,"text":[{"text":"        let mut user = user.ok_or(\"Invalid credentials\")?;","highlight_start":13,"highlight_end":17}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable does not need to be mutable\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domain/services/auth_service.rs:38:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m38\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut user = user.ok_or(\"Invalid credentials\")?;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove this `mut`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_mut)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `user_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/domain/services/auth_service.rs","byte_start":3600,"byte_end":3607,"line_start":105,"line_end":105,"column_start":32,"column_end":39,"is_primary":true,"text":[{"text":"    pub async fn logout(&self, user_id: Uuid, token: &str) -> Result<(), Box<dyn std::error::Error>> {","highlight_start":32,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/domain/services/auth_service.rs","byte_start":3600,"byte_end":3607,"line_start":105,"line_end":105,"column_start":32,"column_end":39,"is_primary":true,"text":[{"text":"    pub async fn logout(&self, user_id: Uuid, token: &str) -> Result<(), Box<dyn std::error::Error>> {","highlight_start":32,"highlight_end":39}],"label":null,"suggested_replacement":"_user_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `user_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domain/services/auth_service.rs:105:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m105\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0mpub async fn logout(&self, user_id: Uuid, token: &str) -> Result<(), Box<dyn\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_user_id`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `token`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/domain/services/auth_service.rs","byte_start":4908,"byte_end":4913,"line_start":137,"line_end":137,"column_start":38,"column_end":43,"is_primary":true,"text":[{"text":"    async fn invalidate_token(&self, token: &str) -> Result<(), Box<dyn std::error::Error>> {","highlight_start":38,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/domain/services/auth_service.rs","byte_start":4908,"byte_end":4913,"line_start":137,"line_end":137,"column_start":38,"column_end":43,"is_primary":true,"text":[{"text":"    async fn invalidate_token(&self, token: &str) -> Result<(), Box<dyn std::error::Error>> {","highlight_start":38,"highlight_end":43}],"label":null,"suggested_replacement":"_token","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `token`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domain/services/auth_service.rs:137:38\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m137\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0mc fn invalidate_token(&self, token: &str) -> Result<(), Box<dyn std::error::\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_token`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"src/domain/services/form_service.rs","byte_start":2405,"byte_end":2413,"line_start":60,"line_end":60,"column_start":13,"column_end":21,"is_primary":true,"text":[{"text":"        let mut form = self.form_repository.find_by_id(id).await?","highlight_start":13,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"src/domain/services/form_service.rs","byte_start":2405,"byte_end":2409,"line_start":60,"line_end":60,"column_start":13,"column_end":17,"is_primary":true,"text":[{"text":"        let mut form = self.form_repository.find_by_id(id).await?","highlight_start":13,"highlight_end":17}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable does not need to be mutable\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domain/services/form_service.rs:60:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m60\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut form = self.form_repository.find_by_id(id).await?\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `path`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/domain/services/file_service.rs","byte_start":4041,"byte_end":4045,"line_start":109,"line_end":109,"column_start":42,"column_end":46,"is_primary":true,"text":[{"text":"    async fn save_file_to_storage(&self, path: &str, content: Vec<u8>) -> Result<(), Box<dyn std::error::Error>> {","highlight_start":42,"highlight_end":46}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/domain/services/file_service.rs","byte_start":4041,"byte_end":4045,"line_start":109,"line_end":109,"column_start":42,"column_end":46,"is_primary":true,"text":[{"text":"    async fn save_file_to_storage(&self, path: &str, content: Vec<u8>) -> Result<(), Box<dyn std::error::Error>> {","highlight_start":42,"highlight_end":46}],"label":null,"suggested_replacement":"_path","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `path`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domain/services/file_service.rs:109:42\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m109\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m save_file_to_storage(&self, path: &str, content: Vec<u8>) -> Result<(), Box\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_path`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `content`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/domain/services/file_service.rs","byte_start":4053,"byte_end":4060,"line_start":109,"line_end":109,"column_start":54,"column_end":61,"is_primary":true,"text":[{"text":"    async fn save_file_to_storage(&self, path: &str, content: Vec<u8>) -> Result<(), Box<dyn std::error::Error>> {","highlight_start":54,"highlight_end":61}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/domain/services/file_service.rs","byte_start":4053,"byte_end":4060,"line_start":109,"line_end":109,"column_start":54,"column_end":61,"is_primary":true,"text":[{"text":"    async fn save_file_to_storage(&self, path: &str, content: Vec<u8>) -> Result<(), Box<dyn std::error::Error>> {","highlight_start":54,"highlight_end":61}],"label":null,"suggested_replacement":"_content","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `content`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domain/services/file_service.rs:109:54\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m109\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0mstorage(&self, path: &str, content: Vec<u8>) -> Result<(), Box<dyn std::erro\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_content`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `path`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/domain/services/file_service.rs","byte_start":4218,"byte_end":4222,"line_start":114,"line_end":114,"column_start":44,"column_end":48,"is_primary":true,"text":[{"text":"    async fn load_file_from_storage(&self, path: &str) -> Result<Vec<u8>, Box<dyn std::error::Error>> {","highlight_start":44,"highlight_end":48}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/domain/services/file_service.rs","byte_start":4218,"byte_end":4222,"line_start":114,"line_end":114,"column_start":44,"column_end":48,"is_primary":true,"text":[{"text":"    async fn load_file_from_storage(&self, path: &str) -> Result<Vec<u8>, Box<dyn std::error::Error>> {","highlight_start":44,"highlight_end":48}],"label":null,"suggested_replacement":"_path","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `path`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domain/services/file_service.rs:114:44\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m114\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0moad_file_from_storage(&self, path: &str) -> Result<Vec<u8>, Box<dyn std::err\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_path`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `path`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/domain/services/file_service.rs","byte_start":4388,"byte_end":4392,"line_start":119,"line_end":119,"column_start":46,"column_end":50,"is_primary":true,"text":[{"text":"    async fn delete_file_from_storage(&self, path: &str) -> Result<(), Box<dyn std::error::Error>> {","highlight_start":46,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/domain/services/file_service.rs","byte_start":4388,"byte_end":4392,"line_start":119,"line_end":119,"column_start":46,"column_end":50,"is_primary":true,"text":[{"text":"    async fn delete_file_from_storage(&self, path: &str) -> Result<(), Box<dyn std::error::Error>> {","highlight_start":46,"highlight_end":50}],"label":null,"suggested_replacement":"_path","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `path`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domain/services/file_service.rs:119:46\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m119\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0mete_file_from_storage(&self, path: &str) -> Result<(), Box<dyn std::error::E\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_path`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `password`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/domain/value_objects/password.rs","byte_start":242,"byte_end":250,"line_start":13,"line_end":13,"column_start":26,"column_end":34,"is_primary":true,"text":[{"text":"    pub fn verify(&self, password: &str, hash: &str) -> bool {","highlight_start":26,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/domain/value_objects/password.rs","byte_start":242,"byte_end":250,"line_start":13,"line_end":13,"column_start":26,"column_end":34,"is_primary":true,"text":[{"text":"    pub fn verify(&self, password: &str, hash: &str) -> bool {","highlight_start":26,"highlight_end":34}],"label":null,"suggested_replacement":"_password","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `password`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domain/value_objects/password.rs:13:26\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn verify(&self, password: &str, hash: &str) -> bool {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_password`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"18 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 18 warnings emitted\u001b[0m\n\n"}
