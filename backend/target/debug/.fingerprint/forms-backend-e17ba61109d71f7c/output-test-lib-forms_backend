{"$message_type":"diagnostic","message":"unused imports: `CreateUserRequest` and `UpdateUserRequest`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/domain/repositories/user_repository.rs","byte_start":83,"byte_end":100,"line_start":4,"line_end":4,"column_start":37,"column_end":54,"is_primary":true,"text":[{"text":"use crate::domain::entities::{User, CreateUserRequest, UpdateUserRequest};","highlight_start":37,"highlight_end":54}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/domain/repositories/user_repository.rs","byte_start":102,"byte_end":119,"line_start":4,"line_end":4,"column_start":56,"column_end":73,"is_primary":true,"text":[{"text":"use crate::domain::entities::{User, CreateUserRequest, UpdateUserRequest};","highlight_start":56,"highlight_end":73}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/domain/repositories/user_repository.rs","byte_start":81,"byte_end":119,"line_start":4,"line_end":4,"column_start":35,"column_end":73,"is_primary":true,"text":[{"text":"use crate::domain::entities::{User, CreateUserRequest, UpdateUserRequest};","highlight_start":35,"highlight_end":73}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/domain/repositories/user_repository.rs","byte_start":76,"byte_end":77,"line_start":4,"line_end":4,"column_start":30,"column_end":31,"is_primary":true,"text":[{"text":"use crate::domain::entities::{User, CreateUserRequest, UpdateUserRequest};","highlight_start":30,"highlight_end":31}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/domain/repositories/user_repository.rs","byte_start":119,"byte_end":120,"line_start":4,"line_end":4,"column_start":73,"column_end":74,"is_primary":true,"text":[{"text":"use crate::domain::entities::{User, CreateUserRequest, UpdateUserRequest};","highlight_start":73,"highlight_end":74}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `CreateUserRequest` and `UpdateUserRequest`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domain/repositories/user_repository.rs:4:37\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::domain::entities::{User, CreateUserRequest, UpdateUserRequest};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `CreateTenantRequest` and `UpdateTenantRequest`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/domain/repositories/tenant_repository.rs","byte_start":85,"byte_end":104,"line_start":4,"line_end":4,"column_start":39,"column_end":58,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Tenant, CreateTenantRequest, UpdateTenantRequest};","highlight_start":39,"highlight_end":58}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/domain/repositories/tenant_repository.rs","byte_start":106,"byte_end":125,"line_start":4,"line_end":4,"column_start":60,"column_end":79,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Tenant, CreateTenantRequest, UpdateTenantRequest};","highlight_start":60,"highlight_end":79}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/domain/repositories/tenant_repository.rs","byte_start":83,"byte_end":125,"line_start":4,"line_end":4,"column_start":37,"column_end":79,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Tenant, CreateTenantRequest, UpdateTenantRequest};","highlight_start":37,"highlight_end":79}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/domain/repositories/tenant_repository.rs","byte_start":76,"byte_end":77,"line_start":4,"line_end":4,"column_start":30,"column_end":31,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Tenant, CreateTenantRequest, UpdateTenantRequest};","highlight_start":30,"highlight_end":31}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/domain/repositories/tenant_repository.rs","byte_start":125,"byte_end":126,"line_start":4,"line_end":4,"column_start":79,"column_end":80,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Tenant, CreateTenantRequest, UpdateTenantRequest};","highlight_start":79,"highlight_end":80}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `CreateTenantRequest` and `UpdateTenantRequest`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domain/repositories/tenant_repository.rs:4:39\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::domain::entities::{Tenant, CreateTenantRequest, UpdateTenantRequest};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `CreateEmployeeRequest` and `UpdateEmployeeRequest`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/domain/repositories/employee_repository.rs","byte_start":87,"byte_end":108,"line_start":4,"line_end":4,"column_start":41,"column_end":62,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Employee, CreateEmployeeRequest, UpdateEmployeeRequest};","highlight_start":41,"highlight_end":62}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/domain/repositories/employee_repository.rs","byte_start":110,"byte_end":131,"line_start":4,"line_end":4,"column_start":64,"column_end":85,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Employee, CreateEmployeeRequest, UpdateEmployeeRequest};","highlight_start":64,"highlight_end":85}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/domain/repositories/employee_repository.rs","byte_start":85,"byte_end":131,"line_start":4,"line_end":4,"column_start":39,"column_end":85,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Employee, CreateEmployeeRequest, UpdateEmployeeRequest};","highlight_start":39,"highlight_end":85}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/domain/repositories/employee_repository.rs","byte_start":76,"byte_end":77,"line_start":4,"line_end":4,"column_start":30,"column_end":31,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Employee, CreateEmployeeRequest, UpdateEmployeeRequest};","highlight_start":30,"highlight_end":31}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/domain/repositories/employee_repository.rs","byte_start":131,"byte_end":132,"line_start":4,"line_end":4,"column_start":85,"column_end":86,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Employee, CreateEmployeeRequest, UpdateEmployeeRequest};","highlight_start":85,"highlight_end":86}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `CreateEmployeeRequest` and `UpdateEmployeeRequest`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domain/repositories/employee_repository.rs:4:41\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::domain::entities::{Employee, CreateEmployeeRequest, UpdateEmployeeRequest};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `CreateCompanyRequest` and `UpdateCompanyRequest`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/domain/repositories/company_repository.rs","byte_start":86,"byte_end":106,"line_start":4,"line_end":4,"column_start":40,"column_end":60,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Company, CreateCompanyRequest, UpdateCompanyRequest};","highlight_start":40,"highlight_end":60}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/domain/repositories/company_repository.rs","byte_start":108,"byte_end":128,"line_start":4,"line_end":4,"column_start":62,"column_end":82,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Company, CreateCompanyRequest, UpdateCompanyRequest};","highlight_start":62,"highlight_end":82}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/domain/repositories/company_repository.rs","byte_start":84,"byte_end":128,"line_start":4,"line_end":4,"column_start":38,"column_end":82,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Company, CreateCompanyRequest, UpdateCompanyRequest};","highlight_start":38,"highlight_end":82}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/domain/repositories/company_repository.rs","byte_start":76,"byte_end":77,"line_start":4,"line_end":4,"column_start":30,"column_end":31,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Company, CreateCompanyRequest, UpdateCompanyRequest};","highlight_start":30,"highlight_end":31}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/domain/repositories/company_repository.rs","byte_start":128,"byte_end":129,"line_start":4,"line_end":4,"column_start":82,"column_end":83,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Company, CreateCompanyRequest, UpdateCompanyRequest};","highlight_start":82,"highlight_end":83}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `CreateCompanyRequest` and `UpdateCompanyRequest`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domain/repositories/company_repository.rs:4:40\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::domain::entities::{Company, CreateCompanyRequest, UpdateCompanyRequest};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `CreateContactRequest` and `UpdateContactRequest`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/domain/repositories/contact_repository.rs","byte_start":86,"byte_end":106,"line_start":4,"line_end":4,"column_start":40,"column_end":60,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Contact, CreateContactRequest, UpdateContactRequest};","highlight_start":40,"highlight_end":60}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/domain/repositories/contact_repository.rs","byte_start":108,"byte_end":128,"line_start":4,"line_end":4,"column_start":62,"column_end":82,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Contact, CreateContactRequest, UpdateContactRequest};","highlight_start":62,"highlight_end":82}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/domain/repositories/contact_repository.rs","byte_start":84,"byte_end":128,"line_start":4,"line_end":4,"column_start":38,"column_end":82,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Contact, CreateContactRequest, UpdateContactRequest};","highlight_start":38,"highlight_end":82}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/domain/repositories/contact_repository.rs","byte_start":76,"byte_end":77,"line_start":4,"line_end":4,"column_start":30,"column_end":31,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Contact, CreateContactRequest, UpdateContactRequest};","highlight_start":30,"highlight_end":31}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/domain/repositories/contact_repository.rs","byte_start":128,"byte_end":129,"line_start":4,"line_end":4,"column_start":82,"column_end":83,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Contact, CreateContactRequest, UpdateContactRequest};","highlight_start":82,"highlight_end":83}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `CreateContactRequest` and `UpdateContactRequest`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domain/repositories/contact_repository.rs:4:40\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::domain::entities::{Contact, CreateContactRequest, UpdateContactRequest};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `CreateFormRequest`, `FormItem`, and `UpdateFormRequest`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/domain/repositories/form_repository.rs","byte_start":83,"byte_end":100,"line_start":4,"line_end":4,"column_start":37,"column_end":54,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Form, CreateFormRequest, UpdateFormRequest, FormItem};","highlight_start":37,"highlight_end":54}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/domain/repositories/form_repository.rs","byte_start":102,"byte_end":119,"line_start":4,"line_end":4,"column_start":56,"column_end":73,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Form, CreateFormRequest, UpdateFormRequest, FormItem};","highlight_start":56,"highlight_end":73}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/domain/repositories/form_repository.rs","byte_start":121,"byte_end":129,"line_start":4,"line_end":4,"column_start":75,"column_end":83,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Form, CreateFormRequest, UpdateFormRequest, FormItem};","highlight_start":75,"highlight_end":83}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/domain/repositories/form_repository.rs","byte_start":81,"byte_end":129,"line_start":4,"line_end":4,"column_start":35,"column_end":83,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Form, CreateFormRequest, UpdateFormRequest, FormItem};","highlight_start":35,"highlight_end":83}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/domain/repositories/form_repository.rs","byte_start":76,"byte_end":77,"line_start":4,"line_end":4,"column_start":30,"column_end":31,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Form, CreateFormRequest, UpdateFormRequest, FormItem};","highlight_start":30,"highlight_end":31}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/domain/repositories/form_repository.rs","byte_start":129,"byte_end":130,"line_start":4,"line_end":4,"column_start":83,"column_end":84,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Form, CreateFormRequest, UpdateFormRequest, FormItem};","highlight_start":83,"highlight_end":84}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `CreateFormRequest`, `FormItem`, and `UpdateFormRequest`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domain/repositories/form_repository.rs:4:37\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::domain::entities::{Form, CreateFormRequest, UpdateFormRequest, FormItem};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `CreateAttachmentRequest`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/domain/repositories/attachment_repository.rs","byte_start":89,"byte_end":112,"line_start":4,"line_end":4,"column_start":43,"column_end":66,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Attachment, CreateAttachmentRequest};","highlight_start":43,"highlight_end":66}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/domain/repositories/attachment_repository.rs","byte_start":87,"byte_end":112,"line_start":4,"line_end":4,"column_start":41,"column_end":66,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Attachment, CreateAttachmentRequest};","highlight_start":41,"highlight_end":66}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/domain/repositories/attachment_repository.rs","byte_start":76,"byte_end":77,"line_start":4,"line_end":4,"column_start":30,"column_end":31,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Attachment, CreateAttachmentRequest};","highlight_start":30,"highlight_end":31}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/domain/repositories/attachment_repository.rs","byte_start":112,"byte_end":113,"line_start":4,"line_end":4,"column_start":66,"column_end":67,"is_primary":true,"text":[{"text":"use crate::domain::entities::{Attachment, CreateAttachmentRequest};","highlight_start":66,"highlight_end":67}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `CreateAttachmentRequest`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domain/repositories/attachment_repository.rs:4:43\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::domain::entities::{Attachment, CreateAttachmentRequest};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `PaginationRequest`, `Pagination`, and `SearchQuery`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/domain/repositories/attachment_repository.rs","byte_start":150,"byte_end":167,"line_start":5,"line_end":5,"column_start":36,"column_end":53,"is_primary":true,"text":[{"text":"use crate::domain::value_objects::{PaginationRequest, Pagination, SearchQuery};","highlight_start":36,"highlight_end":53}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/domain/repositories/attachment_repository.rs","byte_start":169,"byte_end":179,"line_start":5,"line_end":5,"column_start":55,"column_end":65,"is_primary":true,"text":[{"text":"use crate::domain::value_objects::{PaginationRequest, Pagination, SearchQuery};","highlight_start":55,"highlight_end":65}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/domain/repositories/attachment_repository.rs","byte_start":181,"byte_end":192,"line_start":5,"line_end":5,"column_start":67,"column_end":78,"is_primary":true,"text":[{"text":"use crate::domain::value_objects::{PaginationRequest, Pagination, SearchQuery};","highlight_start":67,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/domain/repositories/attachment_repository.rs","byte_start":115,"byte_end":195,"line_start":5,"line_end":6,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::domain::value_objects::{PaginationRequest, Pagination, SearchQuery};","highlight_start":1,"highlight_end":80},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `PaginationRequest`, `Pagination`, and `SearchQuery`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domain/repositories/attachment_repository.rs:5:36\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::domain::value_objects::{PaginationRequest, Pagination, SearchQuery};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `async_trait::async_trait`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/domain/services/user_service.rs","byte_start":4,"byte_end":28,"line_start":1,"line_end":1,"column_start":5,"column_end":29,"is_primary":true,"text":[{"text":"use async_trait::async_trait;","highlight_start":5,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/domain/services/user_service.rs","byte_start":0,"byte_end":30,"line_start":1,"line_end":2,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use async_trait::async_trait;","highlight_start":1,"highlight_end":30},{"text":"use uuid::Uuid;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `async_trait::async_trait`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domain/services/user_service.rs:1:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse async_trait::async_trait;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::sync::Arc`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/domain/services/file_service.rs","byte_start":4,"byte_end":18,"line_start":1,"line_end":1,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use std::sync::Arc;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/domain/services/file_service.rs","byte_start":0,"byte_end":20,"line_start":1,"line_end":2,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::sync::Arc;","highlight_start":1,"highlight_end":20},{"text":"use uuid::Uuid;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `std::sync::Arc`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domain/services/file_service.rs:1:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::sync::Arc;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `FileValidationResult`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/domain/services/file_service.rs","byte_start":219,"byte_end":239,"line_start":6,"line_end":6,"column_start":60,"column_end":80,"is_primary":true,"text":[{"text":"use crate::infrastructure::security::{FileSecurityService, FileValidationResult};","highlight_start":60,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/domain/services/file_service.rs","byte_start":217,"byte_end":239,"line_start":6,"line_end":6,"column_start":58,"column_end":80,"is_primary":true,"text":[{"text":"use crate::infrastructure::security::{FileSecurityService, FileValidationResult};","highlight_start":58,"highlight_end":80}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/domain/services/file_service.rs","byte_start":197,"byte_end":198,"line_start":6,"line_end":6,"column_start":38,"column_end":39,"is_primary":true,"text":[{"text":"use crate::infrastructure::security::{FileSecurityService, FileValidationResult};","highlight_start":38,"highlight_end":39}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/domain/services/file_service.rs","byte_start":239,"byte_end":240,"line_start":6,"line_end":6,"column_start":80,"column_end":81,"is_primary":true,"text":[{"text":"use crate::infrastructure::security::{FileSecurityService, FileValidationResult};","highlight_start":80,"highlight_end":81}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `FileValidationResult`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domain/services/file_service.rs:6:60\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::infrastructure::security::{FileSecurityService, FileValidationResult};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `error`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/infrastructure/database/performance.rs","byte_start":87,"byte_end":92,"line_start":3,"line_end":3,"column_start":27,"column_end":32,"is_primary":true,"text":[{"text":"use tracing::{info, warn, error};","highlight_start":27,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/infrastructure/database/performance.rs","byte_start":85,"byte_end":92,"line_start":3,"line_end":3,"column_start":25,"column_end":32,"is_primary":true,"text":[{"text":"use tracing::{info, warn, error};","highlight_start":25,"highlight_end":32}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `error`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/infrastructure/database/performance.rs:3:27\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{info, warn, error};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Postgres` and `QueryBuilder`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/infrastructure/database/query_builder.rs","byte_start":11,"byte_end":23,"line_start":1,"line_end":1,"column_start":12,"column_end":24,"is_primary":true,"text":[{"text":"use sqlx::{QueryBuilder, Postgres, Row};","highlight_start":12,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/infrastructure/database/query_builder.rs","byte_start":25,"byte_end":33,"line_start":1,"line_end":1,"column_start":26,"column_end":34,"is_primary":true,"text":[{"text":"use sqlx::{QueryBuilder, Postgres, Row};","highlight_start":26,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/infrastructure/database/query_builder.rs","byte_start":11,"byte_end":35,"line_start":1,"line_end":1,"column_start":12,"column_end":36,"is_primary":true,"text":[{"text":"use sqlx::{QueryBuilder, Postgres, Row};","highlight_start":12,"highlight_end":36}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/infrastructure/database/query_builder.rs","byte_start":10,"byte_end":11,"line_start":1,"line_end":1,"column_start":11,"column_end":12,"is_primary":true,"text":[{"text":"use sqlx::{QueryBuilder, Postgres, Row};","highlight_start":11,"highlight_end":12}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/infrastructure/database/query_builder.rs","byte_start":38,"byte_end":39,"line_start":1,"line_end":1,"column_start":39,"column_end":40,"is_primary":true,"text":[{"text":"use sqlx::{QueryBuilder, Postgres, Row};","highlight_start":39,"highlight_end":40}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `Postgres` and `QueryBuilder`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/infrastructure/database/query_builder.rs:1:12\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse sqlx::{QueryBuilder, Postgres, Row};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::collections::HashMap`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/infrastructure/database/query_builder.rs","byte_start":45,"byte_end":70,"line_start":2,"line_end":2,"column_start":5,"column_end":30,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":5,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/infrastructure/database/query_builder.rs","byte_start":41,"byte_end":72,"line_start":2,"line_end":3,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":1,"highlight_end":31},{"text":"use uuid::Uuid;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `std::collections::HashMap`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/infrastructure/database/query_builder.rs:2:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::collections::HashMap;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Extension`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/infrastructure/web/middleware/error_handling.rs","byte_start":135,"byte_end":144,"line_start":7,"line_end":7,"column_start":5,"column_end":14,"is_primary":true,"text":[{"text":"    Extension,","highlight_start":5,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/infrastructure/web/middleware/error_handling.rs","byte_start":129,"byte_end":144,"line_start":6,"line_end":7,"column_start":23,"column_end":14,"is_primary":true,"text":[{"text":"    response::Response,","highlight_start":23,"highlight_end":24},{"text":"    Extension,","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `Extension`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/infrastructure/web/middleware/error_handling.rs:7:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Extension,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::domain::services::RequestContext`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/infrastructure/web/middleware/error_handling.rs","byte_start":326,"byte_end":365,"line_start":14,"line_end":14,"column_start":5,"column_end":44,"is_primary":true,"text":[{"text":"use crate::domain::services::RequestContext;","highlight_start":5,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/infrastructure/web/middleware/error_handling.rs","byte_start":322,"byte_end":367,"line_start":14,"line_end":15,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::domain::services::RequestContext;","highlight_start":1,"highlight_end":45},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `crate::domain::services::RequestContext`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/infrastructure/web/middleware/error_handling.rs:14:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::domain::services::RequestContext;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `extract::State`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/infrastructure/web/middleware/mod.rs","byte_start":204,"byte_end":218,"line_start":13,"line_end":13,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"    extract::State,","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/infrastructure/web/middleware/mod.rs","byte_start":204,"byte_end":224,"line_start":13,"line_end":14,"column_start":5,"column_end":5,"is_primary":true,"text":[{"text":"    extract::State,","highlight_start":5,"highlight_end":20},{"text":"    middleware,","highlight_start":1,"highlight_end":5}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `extract::State`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/infrastructure/web/middleware/mod.rs:13:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    extract::State,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::collections::hash_map::DefaultHasher`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/infrastructure/security/file_security.rs","byte_start":25,"byte_end":66,"line_start":2,"line_end":2,"column_start":5,"column_end":46,"is_primary":true,"text":[{"text":"use std::collections::hash_map::DefaultHasher;","highlight_start":5,"highlight_end":46}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/infrastructure/security/file_security.rs","byte_start":21,"byte_end":68,"line_start":2,"line_end":3,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::collections::hash_map::DefaultHasher;","highlight_start":1,"highlight_end":47},{"text":"use std::hash::{Hash, Hasher};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `std::collections::hash_map::DefaultHasher`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/infrastructure/security/file_security.rs:2:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::collections::hash_map::DefaultHasher;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `AppError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/infrastructure/security/file_security.rs","byte_start":154,"byte_end":162,"line_start":7,"line_end":7,"column_start":20,"column_end":28,"is_primary":true,"text":[{"text":"use crate::error::{AppError, AppResult};","highlight_start":20,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/infrastructure/security/file_security.rs","byte_start":154,"byte_end":164,"line_start":7,"line_end":7,"column_start":20,"column_end":30,"is_primary":true,"text":[{"text":"use crate::error::{AppError, AppResult};","highlight_start":20,"highlight_end":30}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/infrastructure/security/file_security.rs","byte_start":153,"byte_end":154,"line_start":7,"line_end":7,"column_start":19,"column_end":20,"is_primary":true,"text":[{"text":"use crate::error::{AppError, AppResult};","highlight_start":19,"highlight_end":20}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/infrastructure/security/file_security.rs","byte_start":173,"byte_end":174,"line_start":7,"line_end":7,"column_start":39,"column_end":40,"is_primary":true,"text":[{"text":"use crate::error::{AppError, AppResult};","highlight_start":39,"highlight_end":40}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `AppError`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/infrastructure/security/file_security.rs:7:20\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::error::{AppError, AppResult};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `http::StatusCode`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/presentation/handlers/audit_handler.rs","byte_start":71,"byte_end":87,"line_start":4,"line_end":4,"column_start":5,"column_end":21,"is_primary":true,"text":[{"text":"    http::StatusCode,","highlight_start":5,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/presentation/handlers/audit_handler.rs","byte_start":65,"byte_end":87,"line_start":3,"line_end":4,"column_start":34,"column_end":21,"is_primary":true,"text":[{"text":"    extract::{Path, Query, State},","highlight_start":34,"highlight_end":35},{"text":"    http::StatusCode,","highlight_start":1,"highlight_end":21}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `http::StatusCode`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/presentation/handlers/audit_handler.rs:4:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    http::StatusCode,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `RequestContext`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/presentation/handlers/audit_handler.rs","byte_start":322,"byte_end":336,"line_start":13,"line_end":13,"column_start":45,"column_end":59,"is_primary":true,"text":[{"text":"use crate::domain::services::{AuditService, RequestContext};","highlight_start":45,"highlight_end":59}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/presentation/handlers/audit_handler.rs","byte_start":320,"byte_end":336,"line_start":13,"line_end":13,"column_start":43,"column_end":59,"is_primary":true,"text":[{"text":"use crate::domain::services::{AuditService, RequestContext};","highlight_start":43,"highlight_end":59}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/presentation/handlers/audit_handler.rs","byte_start":307,"byte_end":308,"line_start":13,"line_end":13,"column_start":30,"column_end":31,"is_primary":true,"text":[{"text":"use crate::domain::services::{AuditService, RequestContext};","highlight_start":30,"highlight_end":31}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/presentation/handlers/audit_handler.rs","byte_start":336,"byte_end":337,"line_start":13,"line_end":13,"column_start":59,"column_end":60,"is_primary":true,"text":[{"text":"use crate::domain::services::{AuditService, RequestContext};","highlight_start":59,"highlight_end":60}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `RequestContext`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/presentation/handlers/audit_handler.rs:13:45\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::domain::services::{AuditService, RequestContext};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Deserialize`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/presentation/handlers/health_handler.rs","byte_start":89,"byte_end":100,"line_start":6,"line_end":6,"column_start":13,"column_end":24,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize};","highlight_start":13,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/presentation/handlers/health_handler.rs","byte_start":89,"byte_end":102,"line_start":6,"line_end":6,"column_start":13,"column_end":26,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize};","highlight_start":13,"highlight_end":26}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/presentation/handlers/health_handler.rs","byte_start":88,"byte_end":89,"line_start":6,"line_end":6,"column_start":12,"column_end":13,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize};","highlight_start":12,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/presentation/handlers/health_handler.rs","byte_start":111,"byte_end":112,"line_start":6,"line_end":6,"column_start":35,"column_end":36,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize};","highlight_start":35,"highlight_end":36}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `Deserialize`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/presentation/handlers/health_handler.rs:6:13\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse serde::{Deserialize, Serialize};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::sync::Arc`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/presentation/handlers/health_handler.rs","byte_start":118,"byte_end":132,"line_start":7,"line_end":7,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use std::sync::Arc;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/presentation/handlers/health_handler.rs","byte_start":114,"byte_end":134,"line_start":7,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::sync::Arc;","highlight_start":1,"highlight_end":20},{"text":"use std::time::Instant;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `std::sync::Arc`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/presentation/handlers/health_handler.rs:7:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::sync::Arc;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `http::StatusCode`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/presentation/handlers/database_handler.rs","byte_start":65,"byte_end":81,"line_start":4,"line_end":4,"column_start":5,"column_end":21,"is_primary":true,"text":[{"text":"    http::StatusCode,","highlight_start":5,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/presentation/handlers/database_handler.rs","byte_start":59,"byte_end":81,"line_start":3,"line_end":4,"column_start":28,"column_end":21,"is_primary":true,"text":[{"text":"    extract::{Query, State},","highlight_start":28,"highlight_end":29},{"text":"    http::StatusCode,","highlight_start":1,"highlight_end":21}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `http::StatusCode`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/presentation/handlers/database_handler.rs:4:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    http::StatusCode,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unexpected `cfg` condition value: `validator`","code":{"code":"unexpected_cfgs","explanation":null},"level":"warning","spans":[{"file_name":"src/error.rs","byte_start":19023,"byte_end":19044,"line_start":511,"line_end":511,"column_start":7,"column_end":28,"is_primary":true,"text":[{"text":"#[cfg(feature = \"validator\")]","highlight_start":7,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"no expected values for `feature`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"consider adding `validator` as a feature in `Cargo.toml`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"`#[warn(unexpected_cfgs)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the condition","code":null,"level":"help","spans":[{"file_name":"src/error.rs","byte_start":19023,"byte_end":19044,"line_start":511,"line_end":511,"column_start":7,"column_end":28,"is_primary":true,"text":[{"text":"#[cfg(feature = \"validator\")]","highlight_start":7,"highlight_end":28}],"label":null,"suggested_replacement":"","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unexpected `cfg` condition value: `validator`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/error.rs:511:7\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m511\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[cfg(feature = \"validator\")]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: remove the condition\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: no expected values for `feature`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: consider adding `validator` as a feature in `Cargo.toml`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unexpected_cfgs)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `serde_json::json`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/error.rs","byte_start":91,"byte_end":107,"line_start":6,"line_end":6,"column_start":5,"column_end":21,"is_primary":true,"text":[{"text":"use serde_json::json;","highlight_start":5,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/error.rs","byte_start":87,"byte_end":109,"line_start":6,"line_end":7,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use serde_json::json;","highlight_start":1,"highlight_end":22},{"text":"use std::fmt;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `serde_json::json`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/error.rs:6:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse serde_json::json;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::fmt`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/error.rs","byte_start":113,"byte_end":121,"line_start":7,"line_end":7,"column_start":5,"column_end":13,"is_primary":true,"text":[{"text":"use std::fmt;","highlight_start":5,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/error.rs","byte_start":109,"byte_end":123,"line_start":7,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::fmt;","highlight_start":1,"highlight_end":14},{"text":"use thiserror::Error;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `std::fmt`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/error.rs:7:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::fmt;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `uuid::Uuid`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/error.rs","byte_start":177,"byte_end":187,"line_start":10,"line_end":10,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"use uuid::Uuid;","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/error.rs","byte_start":173,"byte_end":189,"line_start":10,"line_end":11,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use uuid::Uuid;","highlight_start":1,"highlight_end":16},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `uuid::Uuid`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/error.rs:10:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse uuid::Uuid;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"src/domain/services/auth_service.rs","byte_start":1033,"byte_end":1041,"line_start":38,"line_end":38,"column_start":13,"column_end":21,"is_primary":true,"text":[{"text":"        let mut user = user.ok_or(\"Invalid credentials\")?;","highlight_start":13,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_mut)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"src/domain/services/auth_service.rs","byte_start":1033,"byte_end":1037,"line_start":38,"line_end":38,"column_start":13,"column_end":17,"is_primary":true,"text":[{"text":"        let mut user = user.ok_or(\"Invalid credentials\")?;","highlight_start":13,"highlight_end":17}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable does not need to be mutable\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domain/services/auth_service.rs:38:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m38\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut user = user.ok_or(\"Invalid credentials\")?;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove this `mut`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_mut)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `user_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/domain/services/auth_service.rs","byte_start":3600,"byte_end":3607,"line_start":105,"line_end":105,"column_start":32,"column_end":39,"is_primary":true,"text":[{"text":"    pub async fn logout(&self, user_id: Uuid, token: &str) -> Result<(), Box<dyn std::error::Error>> {","highlight_start":32,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/domain/services/auth_service.rs","byte_start":3600,"byte_end":3607,"line_start":105,"line_end":105,"column_start":32,"column_end":39,"is_primary":true,"text":[{"text":"    pub async fn logout(&self, user_id: Uuid, token: &str) -> Result<(), Box<dyn std::error::Error>> {","highlight_start":32,"highlight_end":39}],"label":null,"suggested_replacement":"_user_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `user_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domain/services/auth_service.rs:105:32\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m105\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn logout(&self, user_id: Uuid, token: &str) -> Result<(), Box<dyn std::error::Error>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_user_id`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `token`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/domain/services/auth_service.rs","byte_start":4908,"byte_end":4913,"line_start":137,"line_end":137,"column_start":38,"column_end":43,"is_primary":true,"text":[{"text":"    async fn invalidate_token(&self, token: &str) -> Result<(), Box<dyn std::error::Error>> {","highlight_start":38,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/domain/services/auth_service.rs","byte_start":4908,"byte_end":4913,"line_start":137,"line_end":137,"column_start":38,"column_end":43,"is_primary":true,"text":[{"text":"    async fn invalidate_token(&self, token: &str) -> Result<(), Box<dyn std::error::Error>> {","highlight_start":38,"highlight_end":43}],"label":null,"suggested_replacement":"_token","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `token`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domain/services/auth_service.rs:137:38\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m137\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn invalidate_token(&self, token: &str) -> Result<(), Box<dyn std::error::Error>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_token`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"src/domain/services/form_service.rs","byte_start":2405,"byte_end":2413,"line_start":60,"line_end":60,"column_start":13,"column_end":21,"is_primary":true,"text":[{"text":"        let mut form = self.form_repository.find_by_id(id).await?","highlight_start":13,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"src/domain/services/form_service.rs","byte_start":2405,"byte_end":2409,"line_start":60,"line_end":60,"column_start":13,"column_end":17,"is_primary":true,"text":[{"text":"        let mut form = self.form_repository.find_by_id(id).await?","highlight_start":13,"highlight_end":17}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable does not need to be mutable\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domain/services/form_service.rs:60:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m60\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut form = self.form_repository.find_by_id(id).await?\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `filename`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/infrastructure/security/file_security.rs","byte_start":11577,"byte_end":11585,"line_start":306,"line_end":306,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"        filename: &str,","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/infrastructure/security/file_security.rs","byte_start":11577,"byte_end":11585,"line_start":306,"line_end":306,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"        filename: &str,","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":"_filename","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `filename`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/infrastructure/security/file_security.rs:306:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m306\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        filename: &str,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_filename`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `path`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/infrastructure/metrics/mod.rs","byte_start":9064,"byte_end":9068,"line_start":306,"line_end":306,"column_start":59,"column_end":63,"is_primary":true,"text":[{"text":"    pub async fn record_http_request(&self, method: &str, path: &str, status_code: u16, duration: Duration) {","highlight_start":59,"highlight_end":63}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/infrastructure/metrics/mod.rs","byte_start":9064,"byte_end":9068,"line_start":306,"line_end":306,"column_start":59,"column_end":63,"is_primary":true,"text":[{"text":"    pub async fn record_http_request(&self, method: &str, path: &str, status_code: u16, duration: Duration) {","highlight_start":59,"highlight_end":63}],"label":null,"suggested_replacement":"_path","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `path`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/infrastructure/metrics/mod.rs:306:59\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m306\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn record_http_request(&self, method: &str, path: &str, status_code: u16, duration: Duration) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_path`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/presentation/handlers/audit_handler.rs","byte_start":8479,"byte_end":8484,"line_start":275,"line_end":275,"column_start":11,"column_end":16,"is_primary":true,"text":[{"text":"    State(state): State<AuditHandlerState>,","highlight_start":11,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/presentation/handlers/audit_handler.rs","byte_start":8479,"byte_end":8484,"line_start":275,"line_end":275,"column_start":11,"column_end":16,"is_primary":true,"text":[{"text":"    State(state): State<AuditHandlerState>,","highlight_start":11,"highlight_end":16}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/presentation/handlers/audit_handler.rs:275:11\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m275\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    State(state): State<AuditHandlerState>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `audit_log_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/presentation/handlers/audit_handler.rs","byte_start":8575,"byte_end":8587,"line_start":277,"line_end":277,"column_start":10,"column_end":22,"is_primary":true,"text":[{"text":"    Path(audit_log_id): Path<Uuid>,","highlight_start":10,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/presentation/handlers/audit_handler.rs","byte_start":8575,"byte_end":8587,"line_start":277,"line_end":277,"column_start":10,"column_end":22,"is_primary":true,"text":[{"text":"    Path(audit_log_id): Path<Uuid>,","highlight_start":10,"highlight_end":22}],"label":null,"suggested_replacement":"_audit_log_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `audit_log_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/presentation/handlers/audit_handler.rs:277:10\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m277\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Path(audit_log_id): Path<Uuid>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_audit_log_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `start_time`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/presentation/handlers/health_handler.rs","byte_start":1619,"byte_end":1629,"line_start":63,"line_end":63,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"    let start_time = Instant::now();","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/presentation/handlers/health_handler.rs","byte_start":1619,"byte_end":1629,"line_start":63,"line_end":63,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"    let start_time = Instant::now();","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":"_start_time","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `start_time`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/presentation/handlers/health_handler.rs:63:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m63\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let start_time = Instant::now();\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_start_time`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"src/presentation/handlers/file_handler.rs","byte_start":5226,"byte_end":5238,"line_start":173,"line_end":173,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"    let mut response = Response::builder()","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"src/presentation/handlers/file_handler.rs","byte_start":5226,"byte_end":5230,"line_start":173,"line_end":173,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    let mut response = Response::builder()","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable does not need to be mutable\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/presentation/handlers/file_handler.rs:173:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m173\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut response = Response::builder()\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/presentation/handlers/file_handler.rs","byte_start":7657,"byte_end":7662,"line_start":247,"line_end":247,"column_start":11,"column_end":16,"is_primary":true,"text":[{"text":"    State(state): State<FileHandlerState>,","highlight_start":11,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/presentation/handlers/file_handler.rs","byte_start":7657,"byte_end":7662,"line_start":247,"line_end":247,"column_start":11,"column_end":16,"is_primary":true,"text":[{"text":"    State(state): State<FileHandlerState>,","highlight_start":11,"highlight_end":16}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/presentation/handlers/file_handler.rs:247:11\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m247\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    State(state): State<FileHandlerState>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `current_user`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/presentation/handlers/file_handler.rs","byte_start":7704,"byte_end":7716,"line_start":248,"line_end":248,"column_start":15,"column_end":27,"is_primary":true,"text":[{"text":"    Extension(current_user): Extension<CurrentUser>,","highlight_start":15,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/presentation/handlers/file_handler.rs","byte_start":7704,"byte_end":7716,"line_start":248,"line_end":248,"column_start":15,"column_end":27,"is_primary":true,"text":[{"text":"    Extension(current_user): Extension<CurrentUser>,","highlight_start":15,"highlight_end":27}],"label":null,"suggested_replacement":"_current_user","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `current_user`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/presentation/handlers/file_handler.rs:248:15\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m248\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Extension(current_user): Extension<CurrentUser>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_current_user`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `file_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/presentation/handlers/file_handler.rs","byte_start":7752,"byte_end":7759,"line_start":249,"line_end":249,"column_start":10,"column_end":17,"is_primary":true,"text":[{"text":"    Path(file_id): Path<Uuid>,","highlight_start":10,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/presentation/handlers/file_handler.rs","byte_start":7752,"byte_end":7759,"line_start":249,"line_end":249,"column_start":10,"column_end":17,"is_primary":true,"text":[{"text":"    Path(file_id): Path<Uuid>,","highlight_start":10,"highlight_end":17}],"label":null,"suggested_replacement":"_file_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `file_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/presentation/handlers/file_handler.rs:249:10\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m249\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Path(file_id): Path<Uuid>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_file_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `current_user`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/presentation/handlers/file_handler.rs","byte_start":8129,"byte_end":8141,"line_start":259,"line_end":259,"column_start":15,"column_end":27,"is_primary":true,"text":[{"text":"    Extension(current_user): Extension<CurrentUser>,","highlight_start":15,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/presentation/handlers/file_handler.rs","byte_start":8129,"byte_end":8141,"line_start":259,"line_end":259,"column_start":15,"column_end":27,"is_primary":true,"text":[{"text":"    Extension(current_user): Extension<CurrentUser>,","highlight_start":15,"highlight_end":27}],"label":null,"suggested_replacement":"_current_user","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `current_user`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/presentation/handlers/file_handler.rs:259:15\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m259\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Extension(current_user): Extension<CurrentUser>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_current_user`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/presentation/handlers/error_handler.rs","byte_start":3308,"byte_end":3313,"line_start":108,"line_end":108,"column_start":11,"column_end":16,"is_primary":true,"text":[{"text":"    State(state): State<ErrorHandlerState>,","highlight_start":11,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/presentation/handlers/error_handler.rs","byte_start":3308,"byte_end":3313,"line_start":108,"line_end":108,"column_start":11,"column_end":16,"is_primary":true,"text":[{"text":"    State(state): State<ErrorHandlerState>,","highlight_start":11,"highlight_end":16}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/presentation/handlers/error_handler.rs:108:11\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m108\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    State(state): State<ErrorHandlerState>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"src/infrastructure/web/middleware/error_handling.rs","byte_start":10209,"byte_end":10218,"line_start":275,"line_end":275,"column_start":49,"column_end":58,"is_primary":true,"text":[{"text":"        let error = extract_error_from_response(&response);","highlight_start":49,"highlight_end":58}],"label":"expected `&Response<Body>`, found `&Response<()>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/infrastructure/web/middleware/error_handling.rs","byte_start":10181,"byte_end":10208,"line_start":275,"line_end":275,"column_start":21,"column_end":48,"is_primary":false,"text":[{"text":"        let error = extract_error_from_response(&response);","highlight_start":21,"highlight_end":48}],"label":"arguments to this function are incorrect","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected reference `&axum::http::Response<Body>`\n   found reference `&axum::http::Response<()>`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"function defined here","code":null,"level":"note","spans":[{"file_name":"src/infrastructure/web/middleware/error_handling.rs","byte_start":8254,"byte_end":8273,"line_start":229,"line_end":229,"column_start":36,"column_end":55,"is_primary":false,"text":[{"text":"pub fn extract_error_from_response(response: &Response) -> Option<AppError> {","highlight_start":36,"highlight_end":55}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/infrastructure/web/middleware/error_handling.rs","byte_start":8226,"byte_end":8253,"line_start":229,"line_end":229,"column_start":8,"column_end":35,"is_primary":true,"text":[{"text":"pub fn extract_error_from_response(response: &Response) -> Option<AppError> {","highlight_start":8,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m: mismatched types\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/infrastructure/web/middleware/error_handling.rs:275:49\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m275\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let error = extract_error_from_response(&response);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `&Response<Body>`, found `&Response<()>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12marguments to this function are incorrect\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: expected reference `&axum::http::Response<\u001b[0m\u001b[0m\u001b[1m\u001b[35mBody\u001b[0m\u001b[0m>`\u001b[0m\n\u001b[0m               found reference `&axum::http::Response<\u001b[0m\u001b[0m\u001b[1m\u001b[35m()\u001b[0m\u001b[0m>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: function defined here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/infrastructure/web/middleware/error_handling.rs:229:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m229\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub fn extract_error_from_response(response: &Response) -> Option<AppError> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------------\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"type annotations needed for `Result<_, error::AppError>`","code":{"code":"E0282","explanation":"The compiler could not infer a type and asked for a type annotation.\n\nErroneous code example:\n\n```compile_fail,E0282\nlet x = Vec::new();\n```\n\nThis error indicates that type inference did not result in one unique possible\ntype, and extra information is required. In most cases this can be provided\nby adding a type annotation. Sometimes you need to specify a generic type\nparameter manually.\n\nIn the example above, type `Vec` has a type parameter `T`. When calling\n`Vec::new`, barring any other later usage of the variable `x` that allows the\ncompiler to infer what type `T` is, the compiler needs to be told what it is.\n\nThe type can be specified on the variable:\n\n```\nlet x: Vec<i32> = Vec::new();\n```\n\nThe type can also be specified in the path of the expression:\n\n```\nlet x = Vec::<i32>::new();\n```\n\nIn cases with more complex types, it is not necessary to annotate the full\ntype. Once the ambiguity is resolved, the compiler can infer the rest:\n\n```\nlet x: Vec<_> = \"hello\".chars().rev().collect();\n```\n\nAnother way to provide the compiler with enough information, is to specify the\ngeneric type parameter:\n\n```\nlet x = \"hello\".chars().rev().collect::<Vec<char>>();\n```\n\nAgain, you need not specify the full type if the compiler can infer it:\n\n```\nlet x = \"hello\".chars().rev().collect::<Vec<_>>();\n```\n\nApart from a method or function with a generic type parameter, this error can\noccur when a type parameter of a struct or trait cannot be inferred. In that\ncase it is not always possible to use a type annotation, because all candidates\nhave the same return type. For instance:\n\n```compile_fail,E0282\nstruct Foo<T> {\n    num: T,\n}\n\nimpl<T> Foo<T> {\n    fn bar() -> i32 {\n        0\n    }\n\n    fn baz() {\n        let number = Foo::bar();\n    }\n}\n```\n\nThis will fail because the compiler does not know which instance of `Foo` to\ncall `bar` on. Change `Foo::bar()` to `Foo::<T>::bar()` to resolve the error.\n"},"level":"error","spans":[{"file_name":"src/infrastructure/error_recovery.rs","byte_start":13348,"byte_end":13396,"line_start":384,"line_end":384,"column_start":17,"column_end":65,"is_primary":false,"text":[{"text":"                Err(AppError::validation(\"Non-retryable error\"))","highlight_start":17,"highlight_end":65}],"label":"type must be known at this point","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/infrastructure/error_recovery.rs","byte_start":13180,"byte_end":13186,"line_start":381,"line_end":381,"column_start":13,"column_end":19,"is_primary":true,"text":[{"text":"        let result = recovery_service","highlight_start":13,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider giving `result` an explicit type, where the type for type parameter `T` is specified","code":null,"level":"help","spans":[{"file_name":"src/infrastructure/error_recovery.rs","byte_start":13186,"byte_end":13186,"line_start":381,"line_end":381,"column_start":19,"column_end":19,"is_primary":true,"text":[{"text":"        let result = recovery_service","highlight_start":19,"highlight_end":19}],"label":null,"suggested_replacement":": Result<T, _>","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0282]\u001b[0m\u001b[0m\u001b[1m: type annotations needed for `Result<_, error::AppError>`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/infrastructure/error_recovery.rs:381:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m381\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let result = recovery_service\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m384\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                Err(AppError::validation(\"Non-retryable error\"))\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mtype must be known at this point\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider giving `result` an explicit type, where the type for type parameter `T` is specified\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m381\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m        let result\u001b[0m\u001b[0m\u001b[38;5;10m: Result<T, _>\u001b[0m\u001b[0m = recovery_service\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[38;5;10m++++++++++++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Row`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/infrastructure/database/query_builder.rs","byte_start":35,"byte_end":38,"line_start":1,"line_end":1,"column_start":36,"column_end":39,"is_primary":true,"text":[{"text":"use sqlx::{QueryBuilder, Postgres, Row};","highlight_start":36,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `Row`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/infrastructure/database/query_builder.rs:1:36\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse sqlx::{QueryBuilder, Postgres, Row};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Hasher`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/infrastructure/security/file_security.rs","byte_start":90,"byte_end":96,"line_start":3,"line_end":3,"column_start":23,"column_end":29,"is_primary":true,"text":[{"text":"use std::hash::{Hash, Hasher};","highlight_start":23,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `Hasher`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/infrastructure/security/file_security.rs:3:23\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::hash::{Hash, Hasher};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Hash`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/infrastructure/security/file_security.rs","byte_start":84,"byte_end":88,"line_start":3,"line_end":3,"column_start":17,"column_end":21,"is_primary":true,"text":[{"text":"use std::hash::{Hash, Hasher};","highlight_start":17,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `Hash`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/infrastructure/security/file_security.rs:3:17\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::hash::{Hash, Hasher};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `password`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/domain/value_objects/password.rs","byte_start":242,"byte_end":250,"line_start":13,"line_end":13,"column_start":26,"column_end":34,"is_primary":true,"text":[{"text":"    pub fn verify(&self, password: &str, hash: &str) -> bool {","highlight_start":26,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/domain/value_objects/password.rs","byte_start":242,"byte_end":250,"line_start":13,"line_end":13,"column_start":26,"column_end":34,"is_primary":true,"text":[{"text":"    pub fn verify(&self, password: &str, hash: &str) -> bool {","highlight_start":26,"highlight_end":34}],"label":null,"suggested_replacement":"_password","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `password`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/domain/value_objects/password.rs:13:26\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn verify(&self, password: &str, hash: &str) -> bool {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_password`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `bind_index` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"src/infrastructure/database/repositories/audit_repository_impl.rs","byte_start":9384,"byte_end":9394,"line_start":274,"line_end":274,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"            bind_index += 1;","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[warn(unused_assignments)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: value assigned to `bind_index` is never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/infrastructure/database/repositories/audit_repository_impl.rs:274:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m274\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            bind_index += 1;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_assignments)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `content`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/infrastructure/security/file_security.rs","byte_start":17004,"byte_end":17011,"line_start":443,"line_end":443,"column_start":41,"column_end":48,"is_primary":true,"text":[{"text":"    fn check_suspicious_patterns(&self, content: &[u8], filename: &str) -> Vec<SecurityIssue> {","highlight_start":41,"highlight_end":48}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/infrastructure/security/file_security.rs","byte_start":17004,"byte_end":17011,"line_start":443,"line_end":443,"column_start":41,"column_end":48,"is_primary":true,"text":[{"text":"    fn check_suspicious_patterns(&self, content: &[u8], filename: &str) -> Vec<SecurityIssue> {","highlight_start":41,"highlight_end":48}],"label":null,"suggested_replacement":"_content","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `content`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/infrastructure/security/file_security.rs:443:41\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m443\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn check_suspicious_patterns(&self, content: &[u8], filename: &str) -> Vec<SecurityIssue> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_content`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `file_content`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/infrastructure/security/file_security.rs","byte_start":20056,"byte_end":20068,"line_start":530,"line_end":530,"column_start":31,"column_end":43,"is_primary":true,"text":[{"text":"    async fn scan_file(&self, file_content: &[u8], filename: &str) -> AppResult<VirusScanResult> {","highlight_start":31,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/infrastructure/security/file_security.rs","byte_start":20056,"byte_end":20068,"line_start":530,"line_end":530,"column_start":31,"column_end":43,"is_primary":true,"text":[{"text":"    async fn scan_file(&self, file_content: &[u8], filename: &str) -> AppResult<VirusScanResult> {","highlight_start":31,"highlight_end":43}],"label":null,"suggested_replacement":"_file_content","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `file_content`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/infrastructure/security/file_security.rs:530:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m530\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn scan_file(&self, file_content: &[u8], filename: &str) -> AppResult<VirusScanResult> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_file_content`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 2 previous errors; 50 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 2 previous errors; 50 warnings emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0282, E0308.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mSome errors have detailed explanations: E0282, E0308.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0282`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mFor more information about an error, try `rustc --explain E0282`.\u001b[0m\n"}
