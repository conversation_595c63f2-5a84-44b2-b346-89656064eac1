{"rustc": 15497389221046826682, "features": "[\"clap\", \"cli\", \"default\", \"dotenvy\", \"sea-orm-cli\"]", "declared_features": "[\"clap\", \"cli\", \"default\", \"dotenvy\", \"runtime-actix-native-tls\", \"runtime-actix-rustls\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"sea-orm-cli\", \"sqlite-use-returning-for-3_35\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"with-bigdecimal\", \"with-chrono\", \"with-json\", \"with-rust_decimal\", \"with-time\", \"with-uuid\"]", "target": 4990465507743545796, "profile": 8276155916380437441, "path": 5852111874642303627, "deps": [[2706460456408817945, "futures", false, 13953751344136263629], [3405707034081185165, "dotenvy", false, 12881068682361626073], [5943134082002573445, "sea_orm", false, 7323634532078137369], [8606274917505247608, "tracing", false, 6686055793566679718], [9872881177075349226, "sea_schema", false, 9910448373469511917], [11946729385090170470, "async_trait", false, 12171934299148525566], [16230660778393187092, "tracing_subscriber", false, 12841060482171449234], [16942914716228153572, "sea_orm_cli", false, 8971567948273315026], [17791399664576300066, "clap", false, 16949633443766777395]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sea-orm-migration-110f032d4e857d8b/dep-lib-sea_orm_migration", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}