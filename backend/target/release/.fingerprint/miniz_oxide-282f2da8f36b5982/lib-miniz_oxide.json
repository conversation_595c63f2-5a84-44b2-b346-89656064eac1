{"rustc": 15497389221046826682, "features": "[\"with-alloc\"]", "declared_features": "[\"alloc\", \"block-boundary\", \"core\", \"default\", \"rustc-dep-of-std\", \"serde\", \"simd\", \"simd-adler32\", \"std\", \"with-alloc\"]", "target": 8661567070972402511, "profile": 5627820096486484124, "path": 11779607484550266044, "deps": [[7911289239703230891, "adler2", false, 18228021198654555003]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/miniz_oxide-282f2da8f36b5982/dep-lib-miniz_oxide", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}