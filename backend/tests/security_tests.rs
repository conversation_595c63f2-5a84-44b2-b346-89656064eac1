#[cfg(test)]
mod security_tests {
    use super::*;
    use axum::http::{HeaderMap, HeaderValue, StatusCode};
    use axum_test::TestServer;
    use serde_json::json;
    use std::collections::HashMap;

    /// Test suite for security-related functionality
    /// These tests verify that security controls are properly implemented

    #[tokio::test]
    async fn test_sql_injection_protection() {
        let app = create_test_app().await;
        let server = TestServer::new(app).unwrap();

        // Test SQL injection in login endpoint
        let malicious_payloads = vec![
            "admin'; DROP TABLE users; --",
            "' OR '1'='1",
            "' UNION SELECT * FROM users --",
            "admin'/**/OR/**/1=1--",
            "'; EXEC xp_cmdshell('dir'); --",
        ];

        for payload in malicious_payloads {
            let response = server
                .post("/api/auth/login")
                .json(&json!({
                    "email": payload,
                    "password": "password123"
                }))
                .await;

            // Should return 400 or 401, not 500 (which might indicate SQL injection)
            assert!(
                response.status_code() == StatusCode::BAD_REQUEST 
                || response.status_code() == StatusCode::UNAUTHORIZED,
                "SQL injection payload should be handled safely: {}",
                payload
            );
        }
    }

    #[tokio::test]
    async fn test_xss_protection() {
        let app = create_test_app().await;
        let server = TestServer::new(app).unwrap();

        let xss_payloads = vec![
            "<script>alert('xss')</script>",
            "javascript:alert('xss')",
            "<img src=x onerror=alert('xss')>",
            "';alert('xss');//",
            "<svg onload=alert('xss')>",
        ];

        // Test XSS in form creation
        for payload in xss_payloads {
            let response = server
                .post("/api/forms")
                .json(&json!({
                    "title": payload,
                    "description": "Test form",
                    "fields": []
                }))
                .await;

            // Should either reject the input or sanitize it
            if response.status_code() == StatusCode::CREATED {
                let form: serde_json::Value = response.json();
                let title = form["title"].as_str().unwrap_or("");
                
                // Ensure dangerous characters are escaped or removed
                assert!(
                    !title.contains("<script>") && !title.contains("javascript:"),
                    "XSS payload should be sanitized: {}",
                    payload
                );
            }
        }
    }

    #[tokio::test]
    async fn test_csrf_protection() {
        let app = create_test_app().await;
        let server = TestServer::new(app).unwrap();

        // Test that state-changing operations require proper CSRF protection
        let response = server
            .post("/api/forms")
            .json(&json!({
                "title": "Test Form",
                "description": "Test",
                "fields": []
            }))
            .await;

        // Without authentication, should be rejected
        assert_eq!(response.status_code(), StatusCode::UNAUTHORIZED);

        // Test with authentication but without CSRF token
        let auth_response = server
            .post("/api/auth/login")
            .json(&json!({
                "email": "<EMAIL>",
                "password": "password123"
            }))
            .await;

        if auth_response.status_code() == StatusCode::OK {
            let auth_data: serde_json::Value = auth_response.json();
            let token = auth_data["access_token"].as_str().unwrap();

            let response = server
                .post("/api/forms")
                .add_header("Authorization", format!("Bearer {}", token))
                .json(&json!({
                    "title": "Test Form",
                    "description": "Test",
                    "fields": []
                }))
                .await;

            // Should succeed with proper authentication
            assert!(response.status_code().is_success());
        }
    }

    #[tokio::test]
    async fn test_rate_limiting() {
        let app = create_test_app().await;
        let server = TestServer::new(app).unwrap();

        // Test rate limiting on login endpoint
        let mut successful_requests = 0;
        let mut rate_limited_requests = 0;

        for i in 0..20 {
            let response = server
                .post("/api/auth/login")
                .json(&json!({
                    "email": format!("test{}@example.com", i),
                    "password": "wrongpassword"
                }))
                .await;

            if response.status_code() == StatusCode::TOO_MANY_REQUESTS {
                rate_limited_requests += 1;
            } else {
                successful_requests += 1;
            }
        }

        // Should have some rate limiting after multiple requests
        assert!(
            rate_limited_requests > 0,
            "Rate limiting should be triggered after multiple requests"
        );
    }

    #[tokio::test]
    async fn test_authentication_bypass() {
        let app = create_test_app().await;
        let server = TestServer::new(app).unwrap();

        // Test various authentication bypass attempts
        let bypass_attempts = vec![
            // No token
            ("", StatusCode::UNAUTHORIZED),
            // Invalid token
            ("Bearer invalid_token", StatusCode::UNAUTHORIZED),
            // Malformed token
            ("Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid", StatusCode::UNAUTHORIZED),
            // Empty bearer
            ("Bearer ", StatusCode::UNAUTHORIZED),
            // Wrong scheme
            ("Basic dGVzdDp0ZXN0", StatusCode::UNAUTHORIZED),
        ];

        for (auth_header, expected_status) in bypass_attempts {
            let mut request = server.get("/api/users/me");
            
            if !auth_header.is_empty() {
                request = request.add_header("Authorization", auth_header);
            }
            
            let response = request.await;
            assert_eq!(
                response.status_code(),
                expected_status,
                "Authentication bypass attempt should fail: {}",
                auth_header
            );
        }
    }

    #[tokio::test]
    async fn test_authorization_controls() {
        let app = create_test_app().await;
        let server = TestServer::new(app).unwrap();

        // Create test users with different roles
        let admin_token = create_test_user(&server, "<EMAIL>", "admin").await;
        let user_token = create_test_user(&server, "<EMAIL>", "user").await;

        // Test admin-only endpoint with regular user
        let response = server
            .get("/api/admin/users")
            .add_header("Authorization", format!("Bearer {}", user_token))
            .await;

        assert_eq!(
            response.status_code(),
            StatusCode::FORBIDDEN,
            "Regular user should not access admin endpoints"
        );

        // Test admin-only endpoint with admin user
        let response = server
            .get("/api/admin/users")
            .add_header("Authorization", format!("Bearer {}", admin_token))
            .await;

        assert!(
            response.status_code().is_success(),
            "Admin user should access admin endpoints"
        );
    }

    #[tokio::test]
    async fn test_input_validation() {
        let app = create_test_app().await;
        let server = TestServer::new(app).unwrap();

        // Test various invalid inputs
        let invalid_inputs = vec![
            // Oversized input
            json!({
                "title": "A".repeat(10000),
                "description": "Test",
                "fields": []
            }),
            // Invalid email format
            json!({
                "email": "not-an-email",
                "password": "password123"
            }),
            // Missing required fields
            json!({
                "description": "Test"
            }),
            // Invalid data types
            json!({
                "title": 123,
                "description": true,
                "fields": "not-an-array"
            }),
        ];

        for invalid_input in invalid_inputs {
            let response = server
                .post("/api/forms")
                .json(&invalid_input)
                .await;

            assert_eq!(
                response.status_code(),
                StatusCode::BAD_REQUEST,
                "Invalid input should be rejected: {:?}",
                invalid_input
            );
        }
    }

    #[tokio::test]
    async fn test_file_upload_security() {
        let app = create_test_app().await;
        let server = TestServer::new(app).unwrap();

        // Test malicious file uploads
        let malicious_files = vec![
            ("malware.exe", b"MZ\x90\x00", "application/octet-stream"),
            ("script.php", b"<?php system($_GET['cmd']); ?>", "application/x-php"),
            ("shell.jsp", b"<%@ page import=\"java.io.*\" %>", "application/x-jsp"),
            ("huge_file.txt", &vec![b'A'; 200_000_000], "text/plain"), // 200MB file
        ];

        for (filename, content, content_type) in malicious_files {
            let response = server
                .post("/api/files/upload")
                .multipart(|form| {
                    form.part("file", axum_test::multipart::Part::bytes(content.to_vec())
                        .file_name(filename)
                        .mime_type(content_type))
                })
                .await;

            // Should reject malicious files
            assert!(
                !response.status_code().is_success(),
                "Malicious file should be rejected: {}",
                filename
            );
        }
    }

    #[tokio::test]
    async fn test_security_headers() {
        let app = create_test_app().await;
        let server = TestServer::new(app).unwrap();

        let response = server.get("/").await;

        // Check for security headers
        let headers = response.headers();

        assert!(
            headers.contains_key("x-frame-options"),
            "X-Frame-Options header should be present"
        );

        assert!(
            headers.contains_key("x-content-type-options"),
            "X-Content-Type-Options header should be present"
        );

        assert!(
            headers.contains_key("x-xss-protection"),
            "X-XSS-Protection header should be present"
        );

        assert!(
            headers.contains_key("strict-transport-security"),
            "HSTS header should be present"
        );

        assert!(
            headers.contains_key("content-security-policy"),
            "CSP header should be present"
        );
    }

    #[tokio::test]
    async fn test_session_security() {
        let app = create_test_app().await;
        let server = TestServer::new(app).unwrap();

        // Test session fixation protection
        let login_response = server
            .post("/api/auth/login")
            .json(&json!({
                "email": "<EMAIL>",
                "password": "password123"
            }))
            .await;

        if login_response.status_code() == StatusCode::OK {
            let auth_data: serde_json::Value = login_response.json();
            let token = auth_data["access_token"].as_str().unwrap();

            // Test token expiration
            tokio::time::sleep(tokio::time::Duration::from_secs(1)).await;

            let response = server
                .get("/api/users/me")
                .add_header("Authorization", format!("Bearer {}", token))
                .await;

            // Token should still be valid (not expired yet)
            assert!(response.status_code().is_success());

            // Test logout invalidates token
            let logout_response = server
                .post("/api/auth/logout")
                .add_header("Authorization", format!("Bearer {}", token))
                .await;

            assert!(logout_response.status_code().is_success());

            // Token should be invalid after logout
            let response = server
                .get("/api/users/me")
                .add_header("Authorization", format!("Bearer {}", token))
                .await;

            assert_eq!(response.status_code(), StatusCode::UNAUTHORIZED);
        }
    }

    #[tokio::test]
    async fn test_data_exposure() {
        let app = create_test_app().await;
        let server = TestServer::new(app).unwrap();

        // Test that sensitive data is not exposed in responses
        let response = server
            .post("/api/auth/login")
            .json(&json!({
                "email": "<EMAIL>",
                "password": "wrongpassword"
            }))
            .await;

        let response_text = response.text();

        // Should not expose sensitive information in error messages
        assert!(
            !response_text.contains("password"),
            "Password should not be exposed in error messages"
        );

        assert!(
            !response_text.contains("database"),
            "Database information should not be exposed"
        );

        assert!(
            !response_text.contains("stack trace"),
            "Stack traces should not be exposed"
        );
    }

    // Helper function to create test users
    async fn create_test_user(server: &TestServer, email: &str, role: &str) -> String {
        let response = server
            .post("/api/auth/register")
            .json(&json!({
                "email": email,
                "password": "password123",
                "name": "Test User",
                "role": role
            }))
            .await;

        if response.status_code() == StatusCode::CREATED {
            let auth_data: serde_json::Value = response.json();
            auth_data["access_token"].as_str().unwrap().to_string()
        } else {
            // Try to login if user already exists
            let login_response = server
                .post("/api/auth/login")
                .json(&json!({
                    "email": email,
                    "password": "password123"
                }))
                .await;

            let auth_data: serde_json::Value = login_response.json();
            auth_data["access_token"].as_str().unwrap().to_string()
        }
    }

    // Helper function to create test app
    async fn create_test_app() -> axum::Router {
        // This would create a test instance of your application
        // with test database and configuration
        todo!("Implement test app creation")
    }

    #[tokio::test]
    async fn test_password_security() {
        // Test password hashing and validation
        let weak_passwords = vec![
            "123456",
            "password",
            "qwerty",
            "abc123",
            "password123",
        ];

        for weak_password in weak_passwords {
            // Test that weak passwords are rejected
            let app = create_test_app().await;
            let server = TestServer::new(app).unwrap();

            let response = server
                .post("/api/auth/register")
                .json(&json!({
                    "email": "<EMAIL>",
                    "password": weak_password,
                    "name": "Test User"
                }))
                .await;

            assert_eq!(
                response.status_code(),
                StatusCode::BAD_REQUEST,
                "Weak password should be rejected: {}",
                weak_password
            );
        }
    }

    #[tokio::test]
    async fn test_timing_attacks() {
        let app = create_test_app().await;
        let server = TestServer::new(app).unwrap();

        // Test that login timing is consistent for valid and invalid users
        let mut valid_user_times = Vec::new();
        let mut invalid_user_times = Vec::new();

        for i in 0..10 {
            // Time login attempt for valid user
            let start = std::time::Instant::now();
            let _response = server
                .post("/api/auth/login")
                .json(&json!({
                    "email": "<EMAIL>",
                    "password": "wrongpassword"
                }))
                .await;
            valid_user_times.push(start.elapsed());

            // Time login attempt for invalid user
            let start = std::time::Instant::now();
            let _response = server
                .post("/api/auth/login")
                .json(&json!({
                    "email": format!("invalid{}@example.com", i),
                    "password": "wrongpassword"
                }))
                .await;
            invalid_user_times.push(start.elapsed());
        }

        // Calculate average times
        let avg_valid = valid_user_times.iter().sum::<std::time::Duration>() / valid_user_times.len() as u32;
        let avg_invalid = invalid_user_times.iter().sum::<std::time::Duration>() / invalid_user_times.len() as u32;

        // Times should be similar to prevent timing attacks
        let time_diff = if avg_valid > avg_invalid {
            avg_valid - avg_invalid
        } else {
            avg_invalid - avg_valid
        };

        assert!(
            time_diff < std::time::Duration::from_millis(100),
            "Login timing should be consistent to prevent timing attacks"
        );
    }
}
