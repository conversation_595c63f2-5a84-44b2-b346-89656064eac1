# FORMS Application Environment Configuration
# Copy this file to .env and update the values for your environment

# =============================================================================
# ENVIRONMENT SETTINGS
# =============================================================================
ENVIRONMENT=production
COMPOSE_PROJECT_NAME=forms

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
POSTGRES_DB=forms
POSTGRES_USER=forms
POSTGRES_PASSWORD=your_secure_postgres_password_here
POSTGRES_PORT=5432

# Database URL (constructed from above values)
DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_PASSWORD=your_secure_redis_password_here
REDIS_PORT=6379
REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379

# =============================================================================
# APPLICATION SECRETS
# =============================================================================
# JWT Secret (generate with: openssl rand -base64 32)
JWT_SECRET=your_jwt_secret_key_here_minimum_32_characters

# NextAuth Secret (generate with: openssl rand -base64 32)
NEXTAUTH_SECRET=your_nextauth_secret_key_here_minimum_32_characters

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
# Backend
BACKEND_PORT=8000
SERVER_HOST=0.0.0.0
SERVER_PORT=8000

# Frontend
FRONTEND_PORT=3000
NEXTAUTH_URL=https://your-domain.com
NEXT_PUBLIC_API_URL=https://your-domain.com/api

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# CORS
CORS_ALLOWED_ORIGINS=https://your-domain.com,https://www.your-domain.com

# HSTS
ENABLE_HSTS=true
HSTS_MAX_AGE=31536000

# Content Security Policy
ENABLE_CSP=true
CSP_POLICY=default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self'; connect-src 'self'; frame-ancestors 'none'; base-uri 'self'; form-action 'self';

# Request validation
ENABLE_REQUEST_VALIDATION=true
MAX_REQUEST_SIZE=104857600

# Blocked IPs and User Agents (comma-separated)
BLOCKED_IPS=
BLOCKED_USER_AGENTS=

# =============================================================================
# RATE LIMITING
# =============================================================================
RATE_LIMITING_ENABLED=true
RATE_LIMIT_GENERAL_MAX=100
RATE_LIMIT_GENERAL_WINDOW=60
RATE_LIMIT_AUTH_MAX=10
RATE_LIMIT_AUTH_WINDOW=60
RATE_LIMIT_UPLOAD_MAX=5
RATE_LIMIT_UPLOAD_WINDOW=60
RATE_LIMIT_EXPORT_MAX=20
RATE_LIMIT_EXPORT_WINDOW=300
RATE_LIMIT_PER_IP=true
RATE_LIMIT_PER_USER=false

# =============================================================================
# FILE STORAGE
# =============================================================================
FILE_STORAGE_PATH=/app/storage
MAX_FILE_SIZE=104857600

# =============================================================================
# LOGGING
# =============================================================================
RUST_LOG=info
RUST_BACKTRACE=0
LOG_LEVEL=info

# =============================================================================
# MONITORING
# =============================================================================
# Grafana
GRAFANA_PASSWORD=your_secure_grafana_password_here

# Prometheus
PROMETHEUS_RETENTION=15d

# =============================================================================
# EMAIL CONFIGURATION (Optional)
# =============================================================================
SMTP_HOST=smtp.your-email-provider.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_email_password_here
SMTP_FROM=<EMAIL>

# =============================================================================
# EXTERNAL SERVICES (Optional)
# =============================================================================
# AWS S3 (for file storage)
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION=us-east-1
AWS_S3_BUCKET=

# Sentry (for error tracking)
SENTRY_DSN=

# =============================================================================
# SSL/TLS CONFIGURATION
# =============================================================================
# SSL certificate paths (relative to docker/ssl/)
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem
SSL_DHPARAM_PATH=/etc/nginx/ssl/dhparam.pem

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
BACKUP_RETENTION_DAYS=30
BACKUP_SCHEDULE=0 2 * * *

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
# Set to true for development environment
DEBUG=false
ENABLE_DEBUG_LOGS=false
ENABLE_DETAILED_ERRORS=false

# Hot reload for development
ENABLE_HOT_RELOAD=false

# =============================================================================
# PERFORMANCE TUNING
# =============================================================================
# Database connection pool
DB_MAX_CONNECTIONS=20
DB_MIN_CONNECTIONS=5

# Redis connection pool
REDIS_MAX_CONNECTIONS=10

# Worker processes
WORKER_PROCESSES=auto

# =============================================================================
# FEATURE FLAGS
# =============================================================================
ENABLE_AUDIT_LOGGING=true
ENABLE_VIRUS_SCANNING=false
ENABLE_FILE_ENCRYPTION=false
ENABLE_TWO_FACTOR_AUTH=false
ENABLE_LDAP_AUTH=false

# =============================================================================
# COMPLIANCE SETTINGS
# =============================================================================
# Data retention
DATA_RETENTION_DAYS=2555  # 7 years
AUDIT_RETENTION_DAYS=2555  # 7 years
LOG_RETENTION_DAYS=90      # 3 months

# Privacy settings
ENABLE_GDPR_COMPLIANCE=true
ENABLE_DATA_ANONYMIZATION=true

# =============================================================================
# NOTIFICATION SETTINGS
# =============================================================================
# Slack notifications
SLACK_WEBHOOK_URL=
SLACK_CHANNEL=#alerts

# Email notifications
ENABLE_EMAIL_NOTIFICATIONS=true
ADMIN_EMAIL=<EMAIL>

# =============================================================================
# TIMEZONE SETTINGS
# =============================================================================
TZ=UTC
DEFAULT_TIMEZONE=UTC

# =============================================================================
# LOCALIZATION
# =============================================================================
DEFAULT_LANGUAGE=en
SUPPORTED_LANGUAGES=en,es,fr,de

# =============================================================================
# CUSTOM DOMAIN SETTINGS
# =============================================================================
DOMAIN_NAME=your-domain.com
SUBDOMAIN=forms
FULL_DOMAIN=${SUBDOMAIN}.${DOMAIN_NAME}
