# FORMS Application Data Retention Policy Configuration
# This file defines retention policies for different types of data

# Global settings
global:
  timezone: "UTC"
  enforcement_enabled: true
  notification_enabled: true
  dry_run_mode: false
  
# Database retention policies
database:
  # Audit logs - Keep for compliance requirements
  audit_logs:
    retention_days: 2555  # 7 years
    archive_after_days: 365  # 1 year
    deletion_batch_size: 1000
    schedule: "0 2 * * 0"  # Weekly on Sunday at 2 AM
    
  # User sessions - Short retention for security
  user_sessions:
    retention_days: 30
    deletion_batch_size: 5000
    schedule: "0 3 * * *"  # Daily at 3 AM
    
  # Form submissions - Business data with long retention
  form_submissions:
    retention_days: 2555  # 7 years
    archive_after_days: 1095  # 3 years
    deletion_batch_size: 500
    schedule: "0 4 * * 0"  # Weekly on Sunday at 4 AM
    
  # File attachments metadata - Linked to form submissions
  file_attachments:
    retention_days: 2555  # 7 years
    archive_after_days: 1095  # 3 years
    deletion_batch_size: 100
    schedule: "0 5 * * 0"  # Weekly on Sunday at 5 AM
    
  # User activity logs - Moderate retention
  user_activity:
    retention_days: 365  # 1 year
    deletion_batch_size: 2000
    schedule: "0 6 * * *"  # Daily at 6 AM
    
  # System logs - Short retention
  system_logs:
    retention_days: 90  # 3 months
    deletion_batch_size: 5000
    schedule: "0 7 * * *"  # Daily at 7 AM
    
  # Error logs - Moderate retention for debugging
  error_logs:
    retention_days: 180  # 6 months
    deletion_batch_size: 1000
    schedule: "0 8 * * *"  # Daily at 8 AM
    
  # Performance metrics - Short retention
  performance_metrics:
    retention_days: 30
    deletion_batch_size: 10000
    schedule: "0 9 * * *"  # Daily at 9 AM
    
  # Security events - Long retention for compliance
  security_events:
    retention_days: 2555  # 7 years
    archive_after_days: 365  # 1 year
    deletion_batch_size: 500
    schedule: "0 10 * * 0"  # Weekly on Sunday at 10 AM

# File storage retention policies
file_storage:
  # Uploaded files - Linked to form submissions
  uploaded_files:
    retention_days: 2555  # 7 years
    archive_after_days: 1095  # 3 years
    move_to_cold_storage_after_days: 365  # 1 year
    schedule: "0 11 * * 0"  # Weekly on Sunday at 11 AM
    
  # Temporary files - Very short retention
  temp_files:
    retention_days: 7
    schedule: "0 12 * * *"  # Daily at 12 PM
    
  # Backup files - Separate retention policy
  backup_files:
    retention_days: 90  # 3 months
    schedule: "0 13 * * *"  # Daily at 1 PM
    
  # Log files - Moderate retention
  log_files:
    retention_days: 90  # 3 months
    compress_after_days: 7
    schedule: "0 14 * * *"  # Daily at 2 PM
    
  # Cache files - Very short retention
  cache_files:
    retention_days: 1
    schedule: "0 */6 * * *"  # Every 6 hours

# Backup retention policies
backups:
  # Database backups
  database_backups:
    retention_days: 90  # 3 months
    keep_weekly_for_months: 12  # Keep weekly backups for 1 year
    keep_monthly_for_years: 2   # Keep monthly backups for 2 years
    schedule: "0 15 * * *"  # Daily at 3 PM
    
  # File storage backups
  file_backups:
    retention_days: 60  # 2 months
    keep_weekly_for_months: 6   # Keep weekly backups for 6 months
    keep_monthly_for_years: 1   # Keep monthly backups for 1 year
    schedule: "0 16 * * *"  # Daily at 4 PM
    
  # Configuration backups
  config_backups:
    retention_days: 180  # 6 months
    keep_weekly_for_months: 12  # Keep weekly backups for 1 year
    schedule: "0 17 * * *"  # Daily at 5 PM

# Archive policies
archival:
  # Archive destination
  destination: "cold_storage"  # Options: cold_storage, tape, cloud_archive
  
  # Compression settings
  compression:
    enabled: true
    algorithm: "gzip"  # Options: gzip, bzip2, xz
    level: 6  # Compression level (1-9)
    
  # Encryption settings
  encryption:
    enabled: true
    algorithm: "AES-256"
    key_rotation_days: 90
    
  # Archive verification
  verification:
    enabled: true
    schedule: "0 18 * * 0"  # Weekly on Sunday at 6 PM
    integrity_check: true

# Compliance settings
compliance:
  # Regulatory requirements
  regulations:
    - name: "GDPR"
      enabled: true
      right_to_be_forgotten: true
      data_portability: true
      
    - name: "SOX"
      enabled: true
      audit_trail_retention: 2555  # 7 years
      
    - name: "HIPAA"
      enabled: false
      minimum_retention: 2190  # 6 years
      
  # Data classification
  classification:
    public:
      retention_days: 365
      
    internal:
      retention_days: 1095  # 3 years
      
    confidential:
      retention_days: 2555  # 7 years
      encryption_required: true
      
    restricted:
      retention_days: 2555  # 7 years
      encryption_required: true
      access_logging_required: true

# Notification settings
notifications:
  # Email notifications
  email:
    enabled: true
    recipients:
      - "<EMAIL>"
      - "<EMAIL>"
    
    # Notification triggers
    triggers:
      - "retention_policy_executed"
      - "archival_completed"
      - "deletion_completed"
      - "policy_violation"
      - "storage_threshold_exceeded"
      
  # Slack notifications
  slack:
    enabled: true
    webhook_url: "${SLACK_WEBHOOK_URL}"
    channel: "#data-retention"
    
    triggers:
      - "policy_violation"
      - "storage_threshold_exceeded"

# Monitoring and reporting
monitoring:
  # Metrics collection
  metrics:
    enabled: true
    retention_execution_time: true
    data_volume_processed: true
    storage_space_freed: true
    
  # Reporting
  reports:
    enabled: true
    schedule: "0 19 * * 0"  # Weekly on Sunday at 7 PM
    
    # Report types
    types:
      - "retention_summary"
      - "compliance_status"
      - "storage_utilization"
      - "archival_status"
      
    # Report delivery
    delivery:
      email: true
      file_export: true
      dashboard: true

# Storage thresholds
storage_thresholds:
  # Database storage
  database:
    warning_threshold_gb: 80
    critical_threshold_gb: 95
    
  # File storage
  files:
    warning_threshold_gb: 500
    critical_threshold_gb: 750
    
  # Backup storage
  backups:
    warning_threshold_gb: 200
    critical_threshold_gb: 300
    
  # Archive storage
  archives:
    warning_threshold_gb: 1000
    critical_threshold_gb: 1500

# Execution settings
execution:
  # Parallel processing
  max_concurrent_jobs: 3
  
  # Performance limits
  max_records_per_batch: 1000
  max_execution_time_minutes: 120
  
  # Error handling
  max_retries: 3
  retry_delay_minutes: 5
  
  # Logging
  log_level: "INFO"
  detailed_logging: true
  
  # Safety checks
  require_confirmation: false
  dry_run_first: false
  backup_before_deletion: true

# Custom policies
custom_policies:
  # Example: GDPR right to be forgotten
  gdpr_deletion:
    description: "Delete user data upon request"
    trigger: "user_deletion_request"
    scope: "all_user_data"
    retention_days: 0  # Immediate deletion
    requires_approval: true
    
  # Example: Emergency data retention
  emergency_retention:
    description: "Extended retention for legal hold"
    trigger: "legal_hold_request"
    scope: "specified_data"
    retention_extension_days: 365
    requires_approval: true
