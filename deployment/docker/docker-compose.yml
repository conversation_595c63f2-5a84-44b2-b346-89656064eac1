version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: forms-postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-forms}
      POSTGRES_USER: ${POSTGRES_USER:-forms}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    networks:
      - forms-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-forms} -d ${POSTGRES_DB:-forms}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: forms-redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    ports:
      - "${REDIS_PORT:-6379}:6379"
    networks:
      - forms-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Backend API
  backend:
    build:
      context: ../../
      dockerfile: deployment/docker/Dockerfile.backend
    container_name: forms-backend
    environment:
      # Database
      DATABASE_URL: postgresql://${POSTGRES_USER:-forms}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB:-forms}
      
      # Redis
      REDIS_URL: redis://:${REDIS_PASSWORD}@redis:6379
      
      # Server
      SERVER_HOST: 0.0.0.0
      SERVER_PORT: 8000
      
      # JWT
      JWT_SECRET: ${JWT_SECRET}
      JWT_EXPIRATION: 3600
      JWT_REFRESH_EXPIRATION: 604800
      
      # File Storage
      FILE_STORAGE_PATH: /app/storage
      MAX_FILE_SIZE: 104857600
      
      # Security
      CORS_ALLOWED_ORIGINS: ${CORS_ALLOWED_ORIGINS:-http://localhost:3000}
      ENABLE_HSTS: true
      ENABLE_CSP: true
      
      # Rate Limiting
      RATE_LIMITING_ENABLED: true
      RATE_LIMIT_GENERAL_MAX: 100
      RATE_LIMIT_AUTH_MAX: 10
      
      # Logging
      RUST_LOG: ${RUST_LOG:-info}
      RUST_BACKTRACE: ${RUST_BACKTRACE:-0}
      
      # Environment
      RUST_ENV: ${ENVIRONMENT:-production}
    volumes:
      - backend_storage:/app/storage
      - backend_logs:/app/logs
    ports:
      - "${BACKEND_PORT:-8000}:8000"
    networks:
      - forms-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # Frontend
  frontend:
    build:
      context: ../../
      dockerfile: deployment/docker/Dockerfile.frontend
    container_name: forms-frontend
    environment:
      NEXT_PUBLIC_API_URL: ${NEXT_PUBLIC_API_URL:-http://localhost:8000}
      NEXTAUTH_URL: ${NEXTAUTH_URL:-http://localhost:3000}
      NEXTAUTH_SECRET: ${NEXTAUTH_SECRET}
      NODE_ENV: production
    ports:
      - "${FRONTEND_PORT:-3000}:3000"
    networks:
      - forms-network
    depends_on:
      backend:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: forms-nginx
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    ports:
      - "80:80"
      - "443:443"
    networks:
      - forms-network
    depends_on:
      - frontend
      - backend
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: forms-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - forms-network
    restart: unless-stopped

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    container_name: forms-grafana
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-admin}
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    ports:
      - "3001:3000"
    networks:
      - forms-network
    depends_on:
      - prometheus
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  backend_storage:
    driver: local
  backend_logs:
    driver: local
  nginx_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  forms-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
