groups:
  - name: forms_application_alerts
    rules:
      # Application Health Alerts
      - alert: FormsBackendDown
        expr: up{job="forms-backend"} == 0
        for: 1m
        labels:
          severity: critical
          service: backend
        annotations:
          summary: "FORMS Backend is down"
          description: "FORMS Backend has been down for more than 1 minute"

      - alert: FormsFrontendDown
        expr: up{job="forms-frontend"} == 0
        for: 1m
        labels:
          severity: critical
          service: frontend
        annotations:
          summary: "FORMS Frontend is down"
          description: "FORMS Frontend has been down for more than 1 minute"

      # High Error Rate Alerts
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
          service: backend
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value | humanizePercentage }} for the last 5 minutes"

      - alert: CriticalErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.2
        for: 2m
        labels:
          severity: critical
          service: backend
        annotations:
          summary: "Critical error rate detected"
          description: "Error rate is {{ $value | humanizePercentage }} for the last 2 minutes"

      # Response Time Alerts
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
          service: backend
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value }}s for the last 5 minutes"

      - alert: VeryHighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 5
        for: 2m
        labels:
          severity: critical
          service: backend
        annotations:
          summary: "Very high response time detected"
          description: "95th percentile response time is {{ $value }}s for the last 2 minutes"

  - name: forms_infrastructure_alerts
    rules:
      # Database Alerts
      - alert: PostgreSQLDown
        expr: up{job="postgres"} == 0
        for: 1m
        labels:
          severity: critical
          service: database
        annotations:
          summary: "PostgreSQL is down"
          description: "PostgreSQL database has been down for more than 1 minute"

      - alert: PostgreSQLHighConnections
        expr: pg_stat_database_numbackends / pg_settings_max_connections > 0.8
        for: 5m
        labels:
          severity: warning
          service: database
        annotations:
          summary: "PostgreSQL high connection usage"
          description: "PostgreSQL is using {{ $value | humanizePercentage }} of available connections"

      - alert: PostgreSQLSlowQueries
        expr: rate(pg_stat_database_tup_returned[5m]) / rate(pg_stat_database_tup_fetched[5m]) < 0.1
        for: 10m
        labels:
          severity: warning
          service: database
        annotations:
          summary: "PostgreSQL slow queries detected"
          description: "Query efficiency is low: {{ $value | humanizePercentage }}"

      # Redis Alerts
      - alert: RedisDown
        expr: up{job="redis"} == 0
        for: 1m
        labels:
          severity: critical
          service: cache
        annotations:
          summary: "Redis is down"
          description: "Redis cache has been down for more than 1 minute"

      - alert: RedisHighMemoryUsage
        expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.9
        for: 5m
        labels:
          severity: warning
          service: cache
        annotations:
          summary: "Redis high memory usage"
          description: "Redis is using {{ $value | humanizePercentage }} of available memory"

      # System Resource Alerts
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is {{ $value }}% on {{ $labels.instance }}"

      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.9
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value | humanizePercentage }} on {{ $labels.instance }}"

      - alert: LowDiskSpace
        expr: (node_filesystem_size_bytes - node_filesystem_free_bytes) / node_filesystem_size_bytes > 0.9
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "Low disk space"
          description: "Disk usage is {{ $value | humanizePercentage }} on {{ $labels.instance }}"

      - alert: CriticalDiskSpace
        expr: (node_filesystem_size_bytes - node_filesystem_free_bytes) / node_filesystem_size_bytes > 0.95
        for: 1m
        labels:
          severity: critical
          service: system
        annotations:
          summary: "Critical disk space"
          description: "Disk usage is {{ $value | humanizePercentage }} on {{ $labels.instance }}"

  - name: forms_business_alerts
    rules:
      # Business Logic Alerts
      - alert: HighFailedLogins
        expr: rate(failed_login_attempts_total[5m]) > 10
        for: 2m
        labels:
          severity: warning
          service: security
        annotations:
          summary: "High failed login attempts"
          description: "{{ $value }} failed login attempts per second in the last 5 minutes"

      - alert: SuspiciousActivity
        expr: rate(security_incidents_total[5m]) > 1
        for: 1m
        labels:
          severity: critical
          service: security
        annotations:
          summary: "Suspicious security activity detected"
          description: "{{ $value }} security incidents per second detected"

      - alert: HighFileUploadFailures
        expr: rate(file_upload_failures_total[5m]) / rate(file_upload_attempts_total[5m]) > 0.5
        for: 5m
        labels:
          severity: warning
          service: files
        annotations:
          summary: "High file upload failure rate"
          description: "File upload failure rate is {{ $value | humanizePercentage }}"

      - alert: CircuitBreakerOpen
        expr: circuit_breaker_state{state="open"} == 1
        for: 1m
        labels:
          severity: warning
          service: backend
        annotations:
          summary: "Circuit breaker is open"
          description: "Circuit breaker for {{ $labels.service }} is open"

  - name: forms_data_alerts
    rules:
      # Data Quality Alerts
      - alert: AuditLogGap
        expr: increase(audit_logs_total[1h]) == 0
        for: 1h
        labels:
          severity: warning
          service: audit
        annotations:
          summary: "No audit logs generated"
          description: "No audit logs have been generated in the last hour"

      - alert: DatabaseReplicationLag
        expr: pg_stat_replication_replay_lag > 60
        for: 5m
        labels:
          severity: warning
          service: database
        annotations:
          summary: "Database replication lag"
          description: "Database replication lag is {{ $value }}s"

      - alert: BackupFailure
        expr: time() - backup_last_success_timestamp > 86400
        for: 1m
        labels:
          severity: critical
          service: backup
        annotations:
          summary: "Backup failure"
          description: "Last successful backup was {{ $value | humanizeDuration }} ago"
