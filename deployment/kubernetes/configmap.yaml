apiVersion: v1
kind: ConfigMap
metadata:
  name: forms-config
  namespace: forms
data:
  # Database configuration
  POSTGRES_DB: "forms"
  POSTGRES_USER: "forms"
  POSTGRES_PORT: "5432"
  
  # Redis configuration
  REDIS_PORT: "6379"
  
  # Server configuration
  SERVER_HOST: "0.0.0.0"
  SERVER_PORT: "8000"
  FRONTEND_PORT: "3000"
  
  # Security configuration
  ENABLE_HSTS: "true"
  ENABLE_CSP: "true"
  ENABLE_REQUEST_VALIDATION: "true"
  MAX_REQUEST_SIZE: "104857600"
  
  # Rate limiting
  RATE_LIMITING_ENABLED: "true"
  RATE_LIMIT_GENERAL_MAX: "100"
  RATE_LIMIT_GENERAL_WINDOW: "60"
  RATE_LIMIT_AUTH_MAX: "10"
  RATE_LIMIT_AUTH_WINDOW: "60"
  RATE_LIMIT_UPLOAD_MAX: "5"
  RATE_LIMIT_UPLOAD_WINDOW: "60"
  RATE_LIMIT_EXPORT_MAX: "20"
  RATE_LIMIT_EXPORT_WINDOW: "300"
  RATE_LIMIT_PER_IP: "true"
  RATE_LIMIT_PER_USER: "false"
  
  # File storage
  FILE_STORAGE_PATH: "/app/storage"
  MAX_FILE_SIZE: "104857600"
  
  # Logging
  RUST_LOG: "info"
  RUST_BACKTRACE: "0"
  LOG_LEVEL: "info"
  
  # Environment
  ENVIRONMENT: "production"
  RUST_ENV: "production"
  NODE_ENV: "production"
  
  # Features
  ENABLE_AUDIT_LOGGING: "true"
  ENABLE_VIRUS_SCANNING: "false"
  ENABLE_FILE_ENCRYPTION: "false"
  
  # Compliance
  DATA_RETENTION_DAYS: "2555"
  AUDIT_RETENTION_DAYS: "2555"
  LOG_RETENTION_DAYS: "90"
  ENABLE_GDPR_COMPLIANCE: "true"
  
  # Timezone
  TZ: "UTC"
  DEFAULT_TIMEZONE: "UTC"
  
  # Localization
  DEFAULT_LANGUAGE: "en"
  SUPPORTED_LANGUAGES: "en,es,fr,de"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
  namespace: forms
data:
  nginx.conf: |
    user nginx;
    worker_processes auto;
    error_log /var/log/nginx/error.log warn;
    pid /var/run/nginx.pid;

    events {
        worker_connections 1024;
        use epoll;
        multi_accept on;
    }

    http {
        include /etc/nginx/mime.types;
        default_type application/octet-stream;

        log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                        '$status $body_bytes_sent "$http_referer" '
                        '"$http_user_agent" "$http_x_forwarded_for" '
                        'rt=$request_time uct="$upstream_connect_time" '
                        'uht="$upstream_header_time" urt="$upstream_response_time"';

        access_log /var/log/nginx/access.log main;

        sendfile on;
        tcp_nopush on;
        tcp_nodelay on;
        keepalive_timeout 65;
        types_hash_max_size 2048;
        server_tokens off;

        gzip on;
        gzip_vary on;
        gzip_proxied any;
        gzip_comp_level 6;
        gzip_types
            text/plain
            text/css
            text/xml
            text/javascript
            application/json
            application/javascript
            application/xml+rss
            application/atom+xml
            image/svg+xml;

        # Rate limiting
        limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
        limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/s;
        limit_req_zone $binary_remote_addr zone=upload:10m rate=2r/s;

        # Security headers
        add_header X-Frame-Options DENY always;
        add_header X-Content-Type-Options nosniff always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;

        include /etc/nginx/conf.d/*.conf;
    }

  default.conf: |
    upstream backend {
        server forms-backend:8000;
        keepalive 32;
    }

    upstream frontend {
        server forms-frontend:3000;
        keepalive 32;
    }

    server {
        listen 80;
        server_name _;

        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        location /api/ {
            limit_req zone=api burst=20 nodelay;
            
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            proxy_connect_timeout 30s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }

        location /api/auth/ {
            limit_req zone=auth burst=10 nodelay;
            
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /api/files/upload {
            limit_req zone=upload burst=5 nodelay;
            client_max_body_size 100M;
            
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            proxy_request_buffering off;
        }

        location / {
            proxy_pass http://frontend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
        }
    }
