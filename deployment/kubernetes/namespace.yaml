apiVersion: v1
kind: Namespace
metadata:
  name: forms
  labels:
    name: forms
    app: forms-system
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: forms-quota
  namespace: forms
spec:
  hard:
    requests.cpu: "4"
    requests.memory: 8Gi
    limits.cpu: "8"
    limits.memory: 16Gi
    persistentvolumeclaims: "10"
    services: "10"
    secrets: "20"
    configmaps: "20"
---
apiVersion: v1
kind: LimitRange
metadata:
  name: forms-limits
  namespace: forms
spec:
  limits:
  - default:
      cpu: "1"
      memory: "1Gi"
    defaultRequest:
      cpu: "100m"
      memory: "128Mi"
    type: Container
