#!/bin/bash

# FORMS Application Backup Script
# This script handles database backups, file storage backups, and configuration backups

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
DEPLOYMENT_DIR="$PROJECT_ROOT/deployment"
BACKUP_DIR="${BACKUP_DIR:-$DEPLOYMENT_DIR/backups}"
DOCKER_DIR="$DEPLOYMENT_DIR/docker"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Help function
show_help() {
    cat << EOF
FORMS Application Backup Script

Usage: $0 [OPTIONS] COMMAND

Commands:
    backup-db       Create database backup
    backup-files    Create file storage backup
    backup-config   Create configuration backup
    backup-all      Create complete system backup
    restore-db      Restore database from backup
    restore-files   Restore file storage from backup
    restore-config  Restore configuration from backup
    list-backups    List available backups
    cleanup         Clean up old backups
    verify          Verify backup integrity

Options:
    -e, --env FILE      Environment file (default: .env)
    -d, --dir DIR       Backup directory (default: deployment/backups)
    -r, --retention N   Retention days (default: 30)
    -c, --compress      Compress backups
    -v, --verbose       Enable verbose output
    -h, --help          Show this help message

Examples:
    $0 backup-all                    # Create complete backup
    $0 -c backup-db                  # Create compressed database backup
    $0 -d /custom/backup backup-all  # Backup to custom directory
    $0 restore-db backup_20231201    # Restore specific database backup
    $0 cleanup -r 7                  # Clean backups older than 7 days

EOF
}

# Default values
ENV_FILE=".env"
RETENTION_DAYS=30
COMPRESS=false
VERBOSE=false
COMMAND=""
BACKUP_NAME=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--env)
            ENV_FILE="$2"
            shift 2
            ;;
        -d|--dir)
            BACKUP_DIR="$2"
            shift 2
            ;;
        -r|--retention)
            RETENTION_DAYS="$2"
            shift 2
            ;;
        -c|--compress)
            COMPRESS=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        backup-db|backup-files|backup-config|backup-all|restore-db|restore-files|restore-config|list-backups|cleanup|verify)
            COMMAND="$1"
            shift
            ;;
        *)
            if [[ -z "$BACKUP_NAME" && ("$COMMAND" == "restore-db" || "$COMMAND" == "restore-files" || "$COMMAND" == "restore-config") ]]; then
                BACKUP_NAME="$1"
            else
                log_error "Unknown option: $1"
                show_help
                exit 1
            fi
            shift
            ;;
    esac
done

# Check if command is provided
if [[ -z "$COMMAND" ]]; then
    log_error "No command provided"
    show_help
    exit 1
fi

# Set verbose mode
if [[ "$VERBOSE" == "true" ]]; then
    set -x
fi

# Load environment variables
load_environment() {
    if [[ -f "$DEPLOYMENT_DIR/$ENV_FILE" ]]; then
        log_info "Loading environment from $ENV_FILE..."
        set -a
        source "$DEPLOYMENT_DIR/$ENV_FILE"
        set +a
        log_success "Environment loaded"
    else
        log_warning "Environment file not found: $DEPLOYMENT_DIR/$ENV_FILE"
    fi
}

# Create backup directory
create_backup_dir() {
    mkdir -p "$BACKUP_DIR"
    log_info "Backup directory: $BACKUP_DIR"
}

# Generate backup timestamp
get_backup_timestamp() {
    date +"%Y%m%d_%H%M%S"
}

# Database backup
backup_database() {
    log_info "Creating database backup..."
    
    local timestamp=$(get_backup_timestamp)
    local backup_file="db_backup_${timestamp}.sql"
    local backup_path="$BACKUP_DIR/$backup_file"
    
    cd "$DOCKER_DIR"
    
    # Check if PostgreSQL container is running
    if ! docker-compose ps postgres | grep -q "Up"; then
        log_error "PostgreSQL container is not running"
        return 1
    fi
    
    # Create database dump
    log_info "Dumping database to $backup_file..."
    docker-compose exec -T postgres pg_dump \
        -U "${POSTGRES_USER:-forms}" \
        -d "${POSTGRES_DB:-forms}" \
        --verbose \
        --clean \
        --if-exists \
        --create \
        --format=plain > "$backup_path"
    
    # Compress if requested
    if [[ "$COMPRESS" == "true" ]]; then
        log_info "Compressing backup..."
        gzip "$backup_path"
        backup_path="${backup_path}.gz"
        backup_file="${backup_file}.gz"
    fi
    
    # Verify backup
    if [[ -f "$backup_path" ]]; then
        local size=$(du -h "$backup_path" | cut -f1)
        log_success "Database backup created: $backup_file (Size: $size)"
        
        # Create metadata file
        cat > "$BACKUP_DIR/${backup_file}.meta" << EOF
{
    "type": "database",
    "timestamp": "$timestamp",
    "file": "$backup_file",
    "size": "$size",
    "compressed": $COMPRESS,
    "database": "${POSTGRES_DB:-forms}",
    "user": "${POSTGRES_USER:-forms}",
    "created_at": "$(date -Iseconds)"
}
EOF
        
        return 0
    else
        log_error "Database backup failed"
        return 1
    fi
}

# File storage backup
backup_files() {
    log_info "Creating file storage backup..."
    
    local timestamp=$(get_backup_timestamp)
    local backup_file="files_backup_${timestamp}.tar"
    local backup_path="$BACKUP_DIR/$backup_file"
    
    cd "$DOCKER_DIR"
    
    # Check if backend container is running
    if ! docker-compose ps backend | grep -q "Up"; then
        log_error "Backend container is not running"
        return 1
    fi
    
    # Create file storage archive
    log_info "Archiving file storage to $backup_file..."
    docker-compose exec -T backend tar -cf - -C /app storage | cat > "$backup_path"
    
    # Compress if requested
    if [[ "$COMPRESS" == "true" ]]; then
        log_info "Compressing backup..."
        gzip "$backup_path"
        backup_path="${backup_path}.gz"
        backup_file="${backup_file}.gz"
    fi
    
    # Verify backup
    if [[ -f "$backup_path" ]]; then
        local size=$(du -h "$backup_path" | cut -f1)
        log_success "File storage backup created: $backup_file (Size: $size)"
        
        # Create metadata file
        cat > "$BACKUP_DIR/${backup_file}.meta" << EOF
{
    "type": "files",
    "timestamp": "$timestamp",
    "file": "$backup_file",
    "size": "$size",
    "compressed": $COMPRESS,
    "path": "/app/storage",
    "created_at": "$(date -Iseconds)"
}
EOF
        
        return 0
    else
        log_error "File storage backup failed"
        return 1
    fi
}

# Configuration backup
backup_config() {
    log_info "Creating configuration backup..."
    
    local timestamp=$(get_backup_timestamp)
    local backup_file="config_backup_${timestamp}.tar"
    local backup_path="$BACKUP_DIR/$backup_file"
    
    # Create configuration archive
    log_info "Archiving configuration to $backup_file..."
    tar -cf "$backup_path" \
        -C "$DEPLOYMENT_DIR" \
        --exclude="backups" \
        --exclude="*.log" \
        --exclude="node_modules" \
        --exclude=".git" \
        .
    
    # Compress if requested
    if [[ "$COMPRESS" == "true" ]]; then
        log_info "Compressing backup..."
        gzip "$backup_path"
        backup_path="${backup_path}.gz"
        backup_file="${backup_file}.gz"
    fi
    
    # Verify backup
    if [[ -f "$backup_path" ]]; then
        local size=$(du -h "$backup_path" | cut -f1)
        log_success "Configuration backup created: $backup_file (Size: $size)"
        
        # Create metadata file
        cat > "$BACKUP_DIR/${backup_file}.meta" << EOF
{
    "type": "config",
    "timestamp": "$timestamp",
    "file": "$backup_file",
    "size": "$size",
    "compressed": $COMPRESS,
    "path": "$DEPLOYMENT_DIR",
    "created_at": "$(date -Iseconds)"
}
EOF
        
        return 0
    else
        log_error "Configuration backup failed"
        return 1
    fi
}

# Complete system backup
backup_all() {
    log_info "Creating complete system backup..."
    
    local success=true
    
    # Backup database
    if ! backup_database; then
        success=false
    fi
    
    # Backup files
    if ! backup_files; then
        success=false
    fi
    
    # Backup configuration
    if ! backup_config; then
        success=false
    fi
    
    if [[ "$success" == "true" ]]; then
        log_success "Complete system backup completed successfully"
    else
        log_error "Some backups failed"
        return 1
    fi
}

# Database restore
restore_database() {
    if [[ -z "$BACKUP_NAME" ]]; then
        log_error "Backup name required for restore"
        return 1
    fi
    
    log_info "Restoring database from backup: $BACKUP_NAME"
    
    local backup_file="$BACKUP_DIR/${BACKUP_NAME}"
    
    # Check if backup file exists
    if [[ ! -f "$backup_file" ]]; then
        # Try with .sql extension
        if [[ -f "${backup_file}.sql" ]]; then
            backup_file="${backup_file}.sql"
        elif [[ -f "${backup_file}.sql.gz" ]]; then
            backup_file="${backup_file}.sql.gz"
        else
            log_error "Backup file not found: $backup_file"
            return 1
        fi
    fi
    
    cd "$DOCKER_DIR"
    
    # Check if PostgreSQL container is running
    if ! docker-compose ps postgres | grep -q "Up"; then
        log_error "PostgreSQL container is not running"
        return 1
    fi
    
    # Restore database
    log_info "Restoring database from $backup_file..."
    
    if [[ "$backup_file" == *.gz ]]; then
        # Compressed backup
        zcat "$backup_file" | docker-compose exec -T postgres psql \
            -U "${POSTGRES_USER:-forms}" \
            -d "${POSTGRES_DB:-forms}"
    else
        # Uncompressed backup
        docker-compose exec -T postgres psql \
            -U "${POSTGRES_USER:-forms}" \
            -d "${POSTGRES_DB:-forms}" < "$backup_file"
    fi
    
    log_success "Database restored successfully"
}

# List available backups
list_backups() {
    log_info "Available backups in $BACKUP_DIR:"
    
    if [[ ! -d "$BACKUP_DIR" ]]; then
        log_warning "Backup directory does not exist"
        return 0
    fi
    
    echo
    printf "%-30s %-15s %-10s %-20s\n" "BACKUP FILE" "TYPE" "SIZE" "CREATED"
    printf "%-30s %-15s %-10s %-20s\n" "----------" "----" "----" "-------"
    
    for meta_file in "$BACKUP_DIR"/*.meta; do
        if [[ -f "$meta_file" ]]; then
            local file=$(jq -r '.file' "$meta_file" 2>/dev/null || echo "unknown")
            local type=$(jq -r '.type' "$meta_file" 2>/dev/null || echo "unknown")
            local size=$(jq -r '.size' "$meta_file" 2>/dev/null || echo "unknown")
            local created=$(jq -r '.created_at' "$meta_file" 2>/dev/null || echo "unknown")
            
            printf "%-30s %-15s %-10s %-20s\n" "$file" "$type" "$size" "$created"
        fi
    done
    
    echo
}

# Clean up old backups
cleanup_backups() {
    log_info "Cleaning up backups older than $RETENTION_DAYS days..."
    
    if [[ ! -d "$BACKUP_DIR" ]]; then
        log_warning "Backup directory does not exist"
        return 0
    fi
    
    local deleted_count=0
    
    # Find and delete old backup files
    while IFS= read -r -d '' file; do
        rm -f "$file"
        # Also remove corresponding metadata file
        rm -f "${file}.meta"
        ((deleted_count++))
        log_info "Deleted: $(basename "$file")"
    done < <(find "$BACKUP_DIR" -name "*.sql" -o -name "*.tar" -o -name "*.sql.gz" -o -name "*.tar.gz" -type f -mtime +$RETENTION_DAYS -print0)
    
    log_success "Cleaned up $deleted_count old backup files"
}

# Verify backup integrity
verify_backups() {
    log_info "Verifying backup integrity..."
    
    if [[ ! -d "$BACKUP_DIR" ]]; then
        log_warning "Backup directory does not exist"
        return 0
    fi
    
    local verified=0
    local failed=0
    
    for backup_file in "$BACKUP_DIR"/*.sql "$BACKUP_DIR"/*.tar "$BACKUP_DIR"/*.sql.gz "$BACKUP_DIR"/*.tar.gz; do
        if [[ -f "$backup_file" ]]; then
            log_info "Verifying: $(basename "$backup_file")"
            
            if [[ "$backup_file" == *.gz ]]; then
                # Verify compressed file
                if gzip -t "$backup_file" 2>/dev/null; then
                    log_success "✓ $(basename "$backup_file")"
                    ((verified++))
                else
                    log_error "✗ $(basename "$backup_file") - corrupted"
                    ((failed++))
                fi
            else
                # For uncompressed files, just check if they're readable
                if [[ -r "$backup_file" ]]; then
                    log_success "✓ $(basename "$backup_file")"
                    ((verified++))
                else
                    log_error "✗ $(basename "$backup_file") - not readable"
                    ((failed++))
                fi
            fi
        fi
    done
    
    log_info "Verification complete: $verified verified, $failed failed"
    
    if [[ $failed -gt 0 ]]; then
        return 1
    fi
}

# Main execution
main() {
    load_environment
    create_backup_dir
    
    case "$COMMAND" in
        backup-db)
            backup_database
            ;;
        backup-files)
            backup_files
            ;;
        backup-config)
            backup_config
            ;;
        backup-all)
            backup_all
            ;;
        restore-db)
            restore_database
            ;;
        restore-files)
            log_error "File restore not yet implemented"
            exit 1
            ;;
        restore-config)
            log_error "Configuration restore not yet implemented"
            exit 1
            ;;
        list-backups)
            list_backups
            ;;
        cleanup)
            cleanup_backups
            ;;
        verify)
            verify_backups
            ;;
        *)
            log_error "Unknown command: $COMMAND"
            show_help
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
