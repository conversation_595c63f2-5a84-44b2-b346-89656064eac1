#!/bin/bash

# FORMS Application Deployment Script
# This script handles the complete deployment of the FORMS application

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
DEPLOYMENT_DIR="$PROJECT_ROOT/deployment"
DOCKER_DIR="$DEPLOYMENT_DIR/docker"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Help function
show_help() {
    cat << EOF
FORMS Application Deployment Script

Usage: $0 [OPTIONS] COMMAND

Commands:
    build       Build all Docker images
    deploy      Deploy the application stack
    stop        Stop the application stack
    restart     Restart the application stack
    logs        Show application logs
    status      Show application status
    backup      Create database backup
    restore     Restore database from backup
    update      Update application to latest version
    cleanup     Clean up unused Docker resources

Options:
    -e, --env FILE      Environment file (default: .env)
    -p, --profile NAME  Docker Compose profile (default: production)
    -h, --help          Show this help message
    -v, --verbose       Enable verbose output

Examples:
    $0 deploy                    # Deploy with default settings
    $0 -e staging.env deploy     # Deploy with staging environment
    $0 -p development deploy     # Deploy with development profile
    $0 logs backend              # Show backend logs
    $0 backup                    # Create database backup

EOF
}

# Default values
ENV_FILE=".env"
PROFILE="production"
VERBOSE=false
COMMAND=""
SERVICE=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--env)
            ENV_FILE="$2"
            shift 2
            ;;
        -p|--profile)
            PROFILE="$2"
            shift 2
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        build|deploy|stop|restart|logs|status|backup|restore|update|cleanup)
            COMMAND="$1"
            shift
            ;;
        *)
            if [[ -z "$SERVICE" && "$COMMAND" == "logs" ]]; then
                SERVICE="$1"
            else
                log_error "Unknown option: $1"
                show_help
                exit 1
            fi
            shift
            ;;
    esac
done

# Check if command is provided
if [[ -z "$COMMAND" ]]; then
    log_error "No command provided"
    show_help
    exit 1
fi

# Set verbose mode
if [[ "$VERBOSE" == "true" ]]; then
    set -x
fi

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed"
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose is not installed"
        exit 1
    fi
    
    # Check environment file
    if [[ ! -f "$DEPLOYMENT_DIR/$ENV_FILE" ]]; then
        log_error "Environment file not found: $DEPLOYMENT_DIR/$ENV_FILE"
        log_info "Please create the environment file or specify a different one with -e"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Load environment variables
load_environment() {
    log_info "Loading environment from $ENV_FILE..."
    
    # Export variables from env file
    set -a
    source "$DEPLOYMENT_DIR/$ENV_FILE"
    set +a
    
    # Validate required variables
    required_vars=(
        "POSTGRES_PASSWORD"
        "REDIS_PASSWORD"
        "JWT_SECRET"
        "NEXTAUTH_SECRET"
    )
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            log_error "Required environment variable $var is not set"
            exit 1
        fi
    done
    
    log_success "Environment loaded successfully"
}

# Build Docker images
build_images() {
    log_info "Building Docker images..."
    
    cd "$DOCKER_DIR"
    
    # Build backend
    log_info "Building backend image..."
    docker build -f Dockerfile.backend -t forms-backend:latest "$PROJECT_ROOT"
    
    # Build frontend
    log_info "Building frontend image..."
    docker build -f Dockerfile.frontend -t forms-frontend:latest "$PROJECT_ROOT"
    
    log_success "Docker images built successfully"
}

# Deploy application
deploy_application() {
    log_info "Deploying FORMS application..."
    
    cd "$DOCKER_DIR"
    
    # Create necessary directories
    mkdir -p ssl monitoring/grafana/{dashboards,datasources}
    
    # Generate SSL certificates if they don't exist
    if [[ ! -f "ssl/cert.pem" ]]; then
        log_info "Generating self-signed SSL certificates..."
        generate_ssl_certificates
    fi
    
    # Start services
    log_info "Starting services with profile: $PROFILE"
    docker-compose --env-file "../$ENV_FILE" --profile "$PROFILE" up -d
    
    # Wait for services to be healthy
    log_info "Waiting for services to be healthy..."
    wait_for_services
    
    # Run database migrations
    log_info "Running database migrations..."
    run_migrations
    
    log_success "Application deployed successfully"
    log_info "Frontend: https://localhost"
    log_info "Backend API: https://localhost/api"
    log_info "Grafana: http://localhost:3001"
    log_info "Prometheus: http://localhost:9090"
}

# Generate SSL certificates
generate_ssl_certificates() {
    cd "$DOCKER_DIR"
    
    # Generate DH parameters
    openssl dhparam -out ssl/dhparam.pem 2048
    
    # Generate private key
    openssl genrsa -out ssl/key.pem 2048
    
    # Generate certificate
    openssl req -new -x509 -key ssl/key.pem -out ssl/cert.pem -days 365 -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"
    
    log_success "SSL certificates generated"
}

# Wait for services to be healthy
wait_for_services() {
    local max_attempts=60
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        log_info "Health check attempt $attempt/$max_attempts..."
        
        if docker-compose ps | grep -q "healthy"; then
            log_success "Services are healthy"
            return 0
        fi
        
        sleep 5
        ((attempt++))
    done
    
    log_error "Services failed to become healthy within timeout"
    docker-compose logs
    exit 1
}

# Run database migrations
run_migrations() {
    log_info "Running database migrations..."
    
    docker-compose exec -T backend ./forms-backend migrate
    
    log_success "Database migrations completed"
}

# Stop application
stop_application() {
    log_info "Stopping FORMS application..."
    
    cd "$DOCKER_DIR"
    docker-compose --env-file "../$ENV_FILE" down
    
    log_success "Application stopped"
}

# Restart application
restart_application() {
    log_info "Restarting FORMS application..."
    
    stop_application
    deploy_application
    
    log_success "Application restarted"
}

# Show logs
show_logs() {
    cd "$DOCKER_DIR"
    
    if [[ -n "$SERVICE" ]]; then
        log_info "Showing logs for service: $SERVICE"
        docker-compose logs -f "$SERVICE"
    else
        log_info "Showing logs for all services"
        docker-compose logs -f
    fi
}

# Show status
show_status() {
    log_info "Application status:"
    
    cd "$DOCKER_DIR"
    docker-compose ps
    
    echo
    log_info "Service health:"
    docker-compose exec -T backend curl -f http://localhost:8000/health || log_warning "Backend health check failed"
    docker-compose exec -T frontend wget -q --spider http://localhost:3000/api/health || log_warning "Frontend health check failed"
}

# Create backup
create_backup() {
    log_info "Creating database backup..."
    
    local backup_file="backup_$(date +%Y%m%d_%H%M%S).sql"
    local backup_path="$DEPLOYMENT_DIR/backups/$backup_file"
    
    mkdir -p "$DEPLOYMENT_DIR/backups"
    
    cd "$DOCKER_DIR"
    docker-compose exec -T postgres pg_dump -U "${POSTGRES_USER:-forms}" "${POSTGRES_DB:-forms}" > "$backup_path"
    
    log_success "Backup created: $backup_path"
}

# Restore backup
restore_backup() {
    log_info "Restoring database backup..."
    
    # List available backups
    local backup_dir="$DEPLOYMENT_DIR/backups"
    if [[ ! -d "$backup_dir" ]] || [[ -z "$(ls -A "$backup_dir")" ]]; then
        log_error "No backups found in $backup_dir"
        exit 1
    fi
    
    echo "Available backups:"
    ls -la "$backup_dir"
    
    read -p "Enter backup filename: " backup_file
    
    if [[ ! -f "$backup_dir/$backup_file" ]]; then
        log_error "Backup file not found: $backup_file"
        exit 1
    fi
    
    cd "$DOCKER_DIR"
    docker-compose exec -T postgres psql -U "${POSTGRES_USER:-forms}" -d "${POSTGRES_DB:-forms}" < "$backup_dir/$backup_file"
    
    log_success "Database restored from backup"
}

# Update application
update_application() {
    log_info "Updating FORMS application..."
    
    # Pull latest code (if using git)
    if [[ -d "$PROJECT_ROOT/.git" ]]; then
        log_info "Pulling latest code..."
        cd "$PROJECT_ROOT"
        git pull
    fi
    
    # Rebuild images
    build_images
    
    # Restart with new images
    restart_application
    
    log_success "Application updated"
}

# Cleanup unused resources
cleanup_resources() {
    log_info "Cleaning up unused Docker resources..."
    
    docker system prune -f
    docker volume prune -f
    docker network prune -f
    
    log_success "Cleanup completed"
}

# Main execution
main() {
    check_prerequisites
    load_environment
    
    case "$COMMAND" in
        build)
            build_images
            ;;
        deploy)
            build_images
            deploy_application
            ;;
        stop)
            stop_application
            ;;
        restart)
            restart_application
            ;;
        logs)
            show_logs
            ;;
        status)
            show_status
            ;;
        backup)
            create_backup
            ;;
        restore)
            restore_backup
            ;;
        update)
            update_application
            ;;
        cleanup)
            cleanup_resources
            ;;
        *)
            log_error "Unknown command: $COMMAND"
            show_help
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
