#!/bin/bash

# FORMS Application Backup Cron Setup Script
# This script sets up automated backup scheduling using cron

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKUP_SCRIPT="$SCRIPT_DIR/backup.sh"
CRON_USER="${CRON_USER:-root}"
LOG_DIR="/var/log/forms-backup"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Help function
show_help() {
    cat << EOF
FORMS Application Backup Cron Setup

Usage: $0 [OPTIONS] COMMAND

Commands:
    install     Install backup cron jobs
    uninstall   Remove backup cron jobs
    status      Show current cron jobs
    logs        Show backup logs

Options:
    -u, --user USER     Cron user (default: root)
    -l, --log-dir DIR   Log directory (default: /var/log/forms-backup)
    -h, --help          Show this help message

Backup Schedule:
    - Database: Every 6 hours
    - Files: Daily at 2:00 AM
    - Config: Weekly on Sunday at 3:00 AM
    - Cleanup: Daily at 4:00 AM (remove backups older than 30 days)

Examples:
    $0 install                    # Install with default settings
    $0 -u forms install          # Install for 'forms' user
    $0 status                    # Show current cron jobs
    $0 logs                      # Show recent backup logs

EOF
}

# Default values
COMMAND=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -u|--user)
            CRON_USER="$2"
            shift 2
            ;;
        -l|--log-dir)
            LOG_DIR="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        install|uninstall|status|logs)
            COMMAND="$1"
            shift
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Check if command is provided
if [[ -z "$COMMAND" ]]; then
    log_error "No command provided"
    show_help
    exit 1
fi

# Check prerequisites
check_prerequisites() {
    # Check if backup script exists
    if [[ ! -f "$BACKUP_SCRIPT" ]]; then
        log_error "Backup script not found: $BACKUP_SCRIPT"
        exit 1
    fi
    
    # Make backup script executable
    chmod +x "$BACKUP_SCRIPT"
    
    # Check if cron is installed
    if ! command -v crontab &> /dev/null; then
        log_error "crontab command not found. Please install cron."
        exit 1
    fi
    
    # Create log directory
    sudo mkdir -p "$LOG_DIR"
    sudo chown "$CRON_USER:$CRON_USER" "$LOG_DIR" 2>/dev/null || true
    
    log_success "Prerequisites check passed"
}

# Install backup cron jobs
install_cron_jobs() {
    log_info "Installing backup cron jobs for user: $CRON_USER"
    
    # Create temporary cron file
    local temp_cron=$(mktemp)
    
    # Get existing cron jobs (excluding FORMS backup jobs)
    if crontab -u "$CRON_USER" -l 2>/dev/null | grep -v "# FORMS Backup"; then
        crontab -u "$CRON_USER" -l 2>/dev/null | grep -v "# FORMS Backup" > "$temp_cron"
    fi
    
    # Add FORMS backup cron jobs
    cat >> "$temp_cron" << EOF

# FORMS Backup Jobs - Auto-generated by setup-backup-cron.sh
# Database backup every 6 hours
0 */6 * * * $BACKUP_SCRIPT -c backup-db >> $LOG_DIR/database-backup.log 2>&1 # FORMS Backup

# File storage backup daily at 2:00 AM
0 2 * * * $BACKUP_SCRIPT -c backup-files >> $LOG_DIR/files-backup.log 2>&1 # FORMS Backup

# Configuration backup weekly on Sunday at 3:00 AM
0 3 * * 0 $BACKUP_SCRIPT -c backup-config >> $LOG_DIR/config-backup.log 2>&1 # FORMS Backup

# Cleanup old backups daily at 4:00 AM (keep 30 days)
0 4 * * * $BACKUP_SCRIPT cleanup -r 30 >> $LOG_DIR/cleanup.log 2>&1 # FORMS Backup

# Verify backup integrity daily at 5:00 AM
0 5 * * * $BACKUP_SCRIPT verify >> $LOG_DIR/verify.log 2>&1 # FORMS Backup

EOF
    
    # Install the new cron jobs
    crontab -u "$CRON_USER" "$temp_cron"
    
    # Clean up temporary file
    rm "$temp_cron"
    
    log_success "Backup cron jobs installed successfully"
    
    # Show installed jobs
    show_cron_status
}

# Uninstall backup cron jobs
uninstall_cron_jobs() {
    log_info "Removing backup cron jobs for user: $CRON_USER"
    
    # Create temporary cron file
    local temp_cron=$(mktemp)
    
    # Get existing cron jobs (excluding FORMS backup jobs)
    if crontab -u "$CRON_USER" -l 2>/dev/null | grep -v "# FORMS Backup"; then
        crontab -u "$CRON_USER" -l 2>/dev/null | grep -v "# FORMS Backup" > "$temp_cron"
        
        # Install the filtered cron jobs
        crontab -u "$CRON_USER" "$temp_cron"
        
        log_success "Backup cron jobs removed successfully"
    else
        log_warning "No FORMS backup cron jobs found"
    fi
    
    # Clean up temporary file
    rm "$temp_cron"
}

# Show current cron status
show_cron_status() {
    log_info "Current cron jobs for user: $CRON_USER"
    echo
    
    if crontab -u "$CRON_USER" -l 2>/dev/null; then
        echo
        log_info "FORMS backup jobs:"
        crontab -u "$CRON_USER" -l 2>/dev/null | grep "# FORMS Backup" || log_warning "No FORMS backup jobs found"
    else
        log_warning "No cron jobs found for user: $CRON_USER"
    fi
    
    echo
    log_info "Cron service status:"
    if systemctl is-active --quiet cron 2>/dev/null || systemctl is-active --quiet crond 2>/dev/null; then
        log_success "Cron service is running"
    else
        log_warning "Cron service may not be running"
    fi
}

# Show backup logs
show_backup_logs() {
    log_info "Recent backup logs from: $LOG_DIR"
    echo
    
    if [[ ! -d "$LOG_DIR" ]]; then
        log_warning "Log directory does not exist: $LOG_DIR"
        return 0
    fi
    
    # Show recent database backup logs
    if [[ -f "$LOG_DIR/database-backup.log" ]]; then
        echo "=== Database Backup Logs (last 20 lines) ==="
        tail -20 "$LOG_DIR/database-backup.log"
        echo
    fi
    
    # Show recent file backup logs
    if [[ -f "$LOG_DIR/files-backup.log" ]]; then
        echo "=== File Backup Logs (last 20 lines) ==="
        tail -20 "$LOG_DIR/files-backup.log"
        echo
    fi
    
    # Show recent cleanup logs
    if [[ -f "$LOG_DIR/cleanup.log" ]]; then
        echo "=== Cleanup Logs (last 10 lines) ==="
        tail -10 "$LOG_DIR/cleanup.log"
        echo
    fi
    
    # Show recent verification logs
    if [[ -f "$LOG_DIR/verify.log" ]]; then
        echo "=== Verification Logs (last 10 lines) ==="
        tail -10 "$LOG_DIR/verify.log"
        echo
    fi
    
    # Show log file sizes
    echo "=== Log File Sizes ==="
    du -h "$LOG_DIR"/*.log 2>/dev/null || log_info "No log files found"
}

# Create logrotate configuration
create_logrotate_config() {
    log_info "Creating logrotate configuration..."
    
    local logrotate_config="/etc/logrotate.d/forms-backup"
    
    sudo tee "$logrotate_config" > /dev/null << EOF
$LOG_DIR/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $CRON_USER $CRON_USER
    postrotate
        # Send HUP signal to rsyslog if it's running
        /bin/kill -HUP \$(cat /var/run/rsyslogd.pid 2> /dev/null) 2> /dev/null || true
    endscript
}
EOF
    
    log_success "Logrotate configuration created: $logrotate_config"
}

# Test backup jobs
test_backup_jobs() {
    log_info "Testing backup jobs..."
    
    # Test database backup
    log_info "Testing database backup..."
    if "$BACKUP_SCRIPT" -c backup-db; then
        log_success "Database backup test passed"
    else
        log_error "Database backup test failed"
    fi
    
    # Test file backup
    log_info "Testing file backup..."
    if "$BACKUP_SCRIPT" -c backup-files; then
        log_success "File backup test passed"
    else
        log_error "File backup test failed"
    fi
    
    # Test configuration backup
    log_info "Testing configuration backup..."
    if "$BACKUP_SCRIPT" -c backup-config; then
        log_success "Configuration backup test passed"
    else
        log_error "Configuration backup test failed"
    fi
    
    # Test cleanup
    log_info "Testing cleanup (dry run)..."
    if "$BACKUP_SCRIPT" cleanup -r 365; then  # Very old retention for testing
        log_success "Cleanup test passed"
    else
        log_error "Cleanup test failed"
    fi
    
    # Test verification
    log_info "Testing backup verification..."
    if "$BACKUP_SCRIPT" verify; then
        log_success "Verification test passed"
    else
        log_error "Verification test failed"
    fi
}

# Main execution
main() {
    check_prerequisites
    
    case "$COMMAND" in
        install)
            install_cron_jobs
            create_logrotate_config
            log_info "Testing backup jobs..."
            test_backup_jobs
            ;;
        uninstall)
            uninstall_cron_jobs
            ;;
        status)
            show_cron_status
            ;;
        logs)
            show_backup_logs
            ;;
        *)
            log_error "Unknown command: $COMMAND"
            show_help
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
