services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: forms-postgres
    environment:
      POSTGRES_DB: forms_db
      POSTGRES_USER: forms_user
      POSTGRES_PASSWORD: forms_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - forms-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U forms_user -d forms_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: forms-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - forms-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    command: redis-server --appendonly yes --requirepass redis_password

  # Backend API (Rust + Axum)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: forms-backend
    environment:
      - DATABASE_URL=****************************************************/forms_db
      - REDIS_URL=redis://:redis_password@redis:6379
      - JWT_SECRET=your-super-secret-jwt-key-change-in-production
      - RUST_LOG=debug
      - SERVER_HOST=0.0.0.0
      - SERVER_PORT=8000
      - FILE_STORAGE_PATH=/app/storage
    ports:
      - "8000:8000"
    volumes:
      - forms_storage:/app/storage
    networks:
      - forms-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped

  # Frontend (Next.js)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: forms-frontend
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
      - NODE_ENV=development
    ports:
      - "3000:3000"
    # No volumes needed for production build
    networks:
      - forms-network
    depends_on:
      - backend
    restart: unless-stopped

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: forms-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - forms_storage:/var/www/storage:ro
    networks:
      - forms-network
    depends_on:
      - frontend
      - backend
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  forms_storage:

networks:
  forms-network:
    driver: bridge
