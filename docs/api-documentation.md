# FORMS API Documentation

## Overview

The FORMS API is a RESTful API that provides programmatic access to all form management functionality. It supports JSON request/response format and uses JWT-based authentication.

## Base URL

```
Production: https://your-domain.com/api
Development: http://localhost:8000/api
```

## Authentication

### JWT Authentication

All API endpoints (except public ones) require authentication using JWT tokens.

**Headers:**
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

### Obtaining Tokens

**POST** `/auth/login`

```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "expires_in": 3600,
  "token_type": "Bearer",
  "user": {
    "id": "123e4567-e89b-12d3-a456-************",
    "email": "<EMAIL>",
    "name": "John Doe",
    "role": "user"
  }
}
```

### Refreshing Tokens

**POST** `/auth/refresh`

```json
{
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

## Error Handling

### Error Response Format

```json
{
  "error": "VALIDATION_ERROR",
  "message": "The provided data is invalid. Please check your input and try again.",
  "code": "VALIDATION_ERROR",
  "request_id": "req-123e4567-e89b-12d3-a456-************",
  "details": {
    "field_errors": {
      "email": ["Email is required", "Email format is invalid"]
    }
  },
  "timestamp": "2023-12-01T10:30:00Z",
  "help_url": "https://docs.forms-system.com/errors/validation"
}
```

### HTTP Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict
- `422` - Unprocessable Entity
- `429` - Too Many Requests
- `500` - Internal Server Error

## Rate Limiting

API requests are rate-limited to prevent abuse:

- **General API**: 100 requests per minute
- **Authentication**: 10 requests per minute
- **File Upload**: 5 requests per minute

Rate limit headers are included in responses:
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995800
```

## Pagination

List endpoints support pagination using query parameters:

```http
GET /api/forms?page=1&limit=20&sort=created_date&order=desc
```

**Parameters:**
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 20, max: 100)
- `sort` - Sort field
- `order` - Sort order (`asc` or `desc`)

**Response:**
```json
{
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "total_pages": 8,
    "has_next": true,
    "has_prev": false
  }
}
```

## API Endpoints

### Authentication

#### Login
**POST** `/auth/login`

Authenticate user and obtain JWT tokens.

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### Logout
**POST** `/auth/logout`

Invalidate current session.

#### Register
**POST** `/auth/register`

Register a new user account.

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "name": "John Doe",
  "tenant_id": "123e4567-e89b-12d3-a456-************"
}
```

### Users

#### Get Current User
**GET** `/users/me`

Get current authenticated user information.

#### Update User Profile
**PUT** `/users/me`

Update current user's profile.

**Request:**
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "preferences": {
    "language": "en",
    "timezone": "UTC"
  }
}
```

#### List Users
**GET** `/users`

List users in the current tenant (admin only).

**Query Parameters:**
- `page` - Page number
- `limit` - Items per page
- `search` - Search term
- `role` - Filter by role

### Forms

#### Create Form
**POST** `/forms`

Create a new form.

**Request:**
```json
{
  "title": "Contact Form",
  "description": "A simple contact form",
  "fields": [
    {
      "id": "name",
      "type": "text",
      "label": "Full Name",
      "required": true,
      "validation": {
        "min_length": 2,
        "max_length": 100
      }
    },
    {
      "id": "email",
      "type": "email",
      "label": "Email Address",
      "required": true
    },
    {
      "id": "message",
      "type": "textarea",
      "label": "Message",
      "required": true,
      "validation": {
        "max_length": 1000
      }
    }
  ],
  "settings": {
    "is_public": true,
    "allow_multiple_submissions": false,
    "notification_email": "<EMAIL>"
  }
}
```

#### Get Form
**GET** `/forms/{form_id}`

Get form details by ID.

#### Update Form
**PUT** `/forms/{form_id}`

Update form configuration.

#### Delete Form
**DELETE** `/forms/{form_id}`

Delete a form (admin only).

#### List Forms
**GET** `/forms`

List forms accessible to the current user.

**Query Parameters:**
- `page` - Page number
- `limit` - Items per page
- `search` - Search term
- `status` - Filter by status (`draft`, `published`, `archived`)
- `created_by` - Filter by creator

### Form Submissions

#### Submit Form
**POST** `/forms/{form_id}/submit`

Submit a form with data.

**Request:**
```json
{
  "data": {
    "name": "John Doe",
    "email": "<EMAIL>",
    "message": "Hello, this is a test message."
  },
  "files": {
    "attachment": "file_id_from_upload"
  }
}
```

#### Get Submission
**GET** `/submissions/{submission_id}`

Get submission details by ID.

#### List Submissions
**GET** `/forms/{form_id}/submissions`

List submissions for a specific form.

**Query Parameters:**
- `page` - Page number
- `limit` - Items per page
- `from_date` - Filter from date (ISO 8601)
- `to_date` - Filter to date (ISO 8601)
- `status` - Filter by status

#### Export Submissions
**GET** `/forms/{form_id}/submissions/export`

Export form submissions.

**Query Parameters:**
- `format` - Export format (`csv`, `xlsx`, `json`)
- `from_date` - Filter from date
- `to_date` - Filter to date

### File Management

#### Upload File
**POST** `/files/upload`

Upload a file attachment.

**Request:** Multipart form data
- `file` - File to upload
- `entity_type` - Entity type (e.g., "form_submission")
- `entity_id` - Entity ID

**Response:**
```json
{
  "id": "123e4567-e89b-12d3-a456-************",
  "filename": "document.pdf",
  "original_name": "my-document.pdf",
  "file_size": 1024000,
  "mime_type": "application/pdf",
  "download_url": "/api/files/123e4567-e89b-12d3-a456-************/download"
}
```

#### Download File
**GET** `/files/{file_id}/download`

Download a file by ID.

#### Delete File
**DELETE** `/files/{file_id}`

Delete a file (owner or admin only).

### Audit Logs

#### List Audit Logs
**GET** `/audit/logs`

List audit log entries (admin only).

**Query Parameters:**
- `page` - Page number
- `limit` - Items per page
- `user_id` - Filter by user
- `action` - Filter by action
- `entity_type` - Filter by entity type
- `from_date` - Filter from date
- `to_date` - Filter to date

#### Export Audit Logs
**GET** `/audit/logs/export`

Export audit logs (admin only).

### System Health

#### Health Check
**GET** `/health`

Get system health status.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2023-12-01T10:30:00Z",
  "version": "1.0.0",
  "checks": {
    "database": "healthy",
    "redis": "healthy",
    "storage": "healthy"
  }
}
```

#### System Metrics
**GET** `/metrics`

Get system metrics (admin only).

## Webhooks

### Webhook Events

FORMS can send webhook notifications for various events:

- `form.submitted` - New form submission
- `form.created` - New form created
- `form.updated` - Form updated
- `user.registered` - New user registered

### Webhook Configuration

**POST** `/webhooks`

Create a webhook endpoint.

**Request:**
```json
{
  "url": "https://your-app.com/webhooks/forms",
  "events": ["form.submitted", "form.created"],
  "secret": "your-webhook-secret",
  "active": true
}
```

### Webhook Payload

```json
{
  "event": "form.submitted",
  "timestamp": "2023-12-01T10:30:00Z",
  "data": {
    "form_id": "123e4567-e89b-12d3-a456-************",
    "submission_id": "456e7890-e89b-12d3-a456-************",
    "submitted_by": "<EMAIL>",
    "submission_data": {
      "name": "John Doe",
      "email": "<EMAIL>"
    }
  }
}
```

## SDKs and Libraries

### JavaScript/TypeScript
```bash
npm install @forms-system/js-sdk
```

```javascript
import { FormsClient } from '@forms-system/js-sdk';

const client = new FormsClient({
  baseUrl: 'https://your-domain.com/api',
  apiKey: 'your-api-key'
});

// Submit a form
const submission = await client.forms.submit('form-id', {
  name: 'John Doe',
  email: '<EMAIL>'
});
```

### Python
```bash
pip install forms-system-python
```

```python
from forms_system import FormsClient

client = FormsClient(
    base_url='https://your-domain.com/api',
    api_key='your-api-key'
)

# Submit a form
submission = client.forms.submit('form-id', {
    'name': 'John Doe',
    'email': '<EMAIL>'
})
```

## Examples

### Complete Form Submission Flow

1. **Create a form**
2. **Upload files (if needed)**
3. **Submit the form**
4. **Retrieve submission**

```javascript
// 1. Create form
const form = await client.forms.create({
  title: 'Contact Form',
  fields: [
    { id: 'name', type: 'text', label: 'Name', required: true },
    { id: 'email', type: 'email', label: 'Email', required: true }
  ]
});

// 2. Upload file
const file = await client.files.upload(fileData, {
  entity_type: 'form_submission',
  entity_id: form.id
});

// 3. Submit form
const submission = await client.forms.submit(form.id, {
  name: 'John Doe',
  email: '<EMAIL>',
  attachment: file.id
});

// 4. Retrieve submission
const retrievedSubmission = await client.submissions.get(submission.id);
```

## Testing

### Test Environment
```
Base URL: https://test.forms-system.com/api
```

### Test Credentials
```
Email: <EMAIL>
Password: test123
```

### Postman Collection
Download our Postman collection: [FORMS API Collection](./postman/forms-api.json)

---

**API Version**: v1  
**Last Updated**: December 2023  
**Support**: <EMAIL>
