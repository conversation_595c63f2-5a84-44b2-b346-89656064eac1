# FORMS Deployment Guide

## Overview

This guide provides comprehensive instructions for deploying the FORMS application in production environments. It covers various deployment scenarios, from single-server setups to high-availability clusters.

## Prerequisites

### System Requirements

**Minimum Requirements:**
- CPU: 2 cores
- RAM: 4GB
- Storage: 50GB SSD
- Network: 100 Mbps

**Recommended for Production:**
- CPU: 4+ cores
- RAM: 8GB+
- Storage: 100GB+ SSD
- Network: 1 Gbps
- Load balancer
- Database cluster

### Software Dependencies

**Required:**
- Docker 20.10+
- Docker Compose 2.0+
- Git
- OpenSSL (for SSL certificates)

**Optional:**
- Kubernetes 1.20+ (for K8s deployment)
- Terraform (for infrastructure as code)
- Ansible (for configuration management)

### Network Requirements

**Ports:**
- 80 (HTTP - redirects to HTTPS)
- 443 (HTTPS)
- 22 (SSH for management)

**Internal Ports:**
- 5432 (PostgreSQL)
- 6379 (Redis)
- 8000 (Backend API)
- 3000 (Frontend)
- 9090 (Prometheus)
- 3001 (<PERSON>ana)

## Deployment Options

### Option 1: Dock<PERSON> Compose (Recommended for Small-Medium Deployments)

#### Quick Start

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-org/forms-system.git
   cd forms-system
   ```

2. **Configure environment**
   ```bash
   cp deployment/.env.example deployment/.env
   ```

3. **Edit configuration**
   ```bash
   nano deployment/.env
   ```

   **Critical settings to update:**
   ```env
   # Database
   POSTGRES_PASSWORD=your_secure_password_here
   
   # Redis
   REDIS_PASSWORD=your_redis_password_here
   
   # JWT
   JWT_SECRET=your_jwt_secret_32_chars_minimum
   
   # NextAuth
   NEXTAUTH_SECRET=your_nextauth_secret_here
   NEXTAUTH_URL=https://your-domain.com
   
   # API URL
   NEXT_PUBLIC_API_URL=https://your-domain.com/api
   
   # CORS
   CORS_ALLOWED_ORIGINS=https://your-domain.com
   
   # Email (optional)
   SMTP_HOST=smtp.your-provider.com
   SMTP_USER=<EMAIL>
   SMTP_PASSWORD=your-email-password
   ```

4. **Generate SSL certificates**
   ```bash
   cd deployment/docker
   mkdir -p ssl
   
   # Self-signed (for testing)
   openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
     -keyout ssl/key.pem -out ssl/cert.pem \
     -subj "/C=US/ST=State/L=City/O=Org/CN=your-domain.com"
   
   # Generate DH parameters
   openssl dhparam -out ssl/dhparam.pem 2048
   ```

5. **Deploy the application**
   ```bash
   cd deployment/scripts
   chmod +x deploy.sh
   ./deploy.sh deploy
   ```

6. **Verify deployment**
   ```bash
   ./deploy.sh status
   curl -k https://localhost/health
   ```

#### Production SSL Certificates

For production, use Let's Encrypt or commercial certificates:

**Let's Encrypt with Certbot:**
```bash
# Install certbot
sudo apt-get install certbot

# Generate certificate
sudo certbot certonly --standalone -d your-domain.com

# Copy certificates
sudo cp /etc/letsencrypt/live/your-domain.com/fullchain.pem deployment/docker/ssl/cert.pem
sudo cp /etc/letsencrypt/live/your-domain.com/privkey.pem deployment/docker/ssl/key.pem

# Set up auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Option 2: Kubernetes Deployment

#### Prerequisites

- Kubernetes cluster (1.20+)
- kubectl configured
- Helm 3.0+ (optional)

#### Deploy with Kubernetes

1. **Create namespace**
   ```bash
   kubectl apply -f deployment/kubernetes/namespace.yaml
   ```

2. **Create secrets**
   ```bash
   # Database password
   kubectl create secret generic postgres-secret \
     --from-literal=password=your_secure_password \
     -n forms
   
   # Redis password
   kubectl create secret generic redis-secret \
     --from-literal=password=your_redis_password \
     -n forms
   
   # JWT secret
   kubectl create secret generic jwt-secret \
     --from-literal=secret=your_jwt_secret \
     -n forms
   
   # SSL certificates
   kubectl create secret tls forms-tls \
     --cert=ssl/cert.pem \
     --key=ssl/key.pem \
     -n forms
   ```

3. **Apply configurations**
   ```bash
   kubectl apply -f deployment/kubernetes/configmap.yaml
   kubectl apply -f deployment/kubernetes/postgres.yaml
   kubectl apply -f deployment/kubernetes/redis.yaml
   kubectl apply -f deployment/kubernetes/backend.yaml
   kubectl apply -f deployment/kubernetes/frontend.yaml
   kubectl apply -f deployment/kubernetes/nginx.yaml
   kubectl apply -f deployment/kubernetes/ingress.yaml
   ```

4. **Verify deployment**
   ```bash
   kubectl get pods -n forms
   kubectl get services -n forms
   kubectl logs -f deployment/forms-backend -n forms
   ```

### Option 3: Cloud Deployment

#### AWS ECS with Fargate

1. **Build and push images**
   ```bash
   # Build images
   docker build -f deployment/docker/Dockerfile.backend -t forms-backend .
   docker build -f deployment/docker/Dockerfile.frontend -t forms-frontend .
   
   # Tag for ECR
   docker tag forms-backend:latest 123456789012.dkr.ecr.us-east-1.amazonaws.com/forms-backend:latest
   docker tag forms-frontend:latest 123456789012.dkr.ecr.us-east-1.amazonaws.com/forms-frontend:latest
   
   # Push to ECR
   aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 123456789012.dkr.ecr.us-east-1.amazonaws.com
   docker push 123456789012.dkr.ecr.us-east-1.amazonaws.com/forms-backend:latest
   docker push 123456789012.dkr.ecr.us-east-1.amazonaws.com/forms-frontend:latest
   ```

2. **Deploy with Terraform**
   ```bash
   cd deployment/terraform/aws
   terraform init
   terraform plan -var="domain_name=your-domain.com"
   terraform apply
   ```

#### Google Cloud Run

1. **Build and deploy**
   ```bash
   # Enable APIs
   gcloud services enable run.googleapis.com
   gcloud services enable cloudbuild.googleapis.com
   
   # Deploy backend
   gcloud run deploy forms-backend \
     --source=. \
     --dockerfile=deployment/docker/Dockerfile.backend \
     --platform=managed \
     --region=us-central1 \
     --allow-unauthenticated
   
   # Deploy frontend
   gcloud run deploy forms-frontend \
     --source=. \
     --dockerfile=deployment/docker/Dockerfile.frontend \
     --platform=managed \
     --region=us-central1 \
     --allow-unauthenticated
   ```

## Configuration Management

### Environment Variables

**Security Configuration:**
```env
# JWT Configuration
JWT_SECRET=your_32_character_secret_key_here
JWT_EXPIRATION=3600
JWT_REFRESH_EXPIRATION=604800

# Password Requirements
PASSWORD_MIN_LENGTH=8
PASSWORD_REQUIRE_UPPERCASE=true
PASSWORD_REQUIRE_LOWERCASE=true
PASSWORD_REQUIRE_NUMBERS=true
PASSWORD_REQUIRE_SYMBOLS=true

# Rate Limiting
RATE_LIMITING_ENABLED=true
RATE_LIMIT_GENERAL_MAX=100
RATE_LIMIT_AUTH_MAX=10
RATE_LIMIT_UPLOAD_MAX=5

# Security Headers
ENABLE_HSTS=true
ENABLE_CSP=true
CORS_ALLOWED_ORIGINS=https://your-domain.com
```

**Database Configuration:**
```env
# PostgreSQL
DATABASE_URL=*****************************************/forms
DB_MAX_CONNECTIONS=20
DB_MIN_CONNECTIONS=5

# Redis
REDIS_URL=redis://:password@redis:6379
REDIS_MAX_CONNECTIONS=10
```

**File Storage Configuration:**
```env
# Local Storage
FILE_STORAGE_PATH=/app/storage
MAX_FILE_SIZE=104857600

# AWS S3 (optional)
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-forms-bucket
```

### Database Setup

#### Initial Database Setup

```bash
# Run migrations
docker-compose exec backend ./forms-backend migrate

# Create admin user
docker-compose exec backend ./forms-backend create-admin \
  --email <EMAIL> \
  --password admin123 \
  --name "System Administrator"
```

#### Database Optimization

**PostgreSQL Configuration:**
```sql
-- Optimize for forms workload
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;
ALTER SYSTEM SET random_page_cost = 1.1;
SELECT pg_reload_conf();
```

### Monitoring Setup

#### Prometheus Configuration

```yaml
# deployment/docker/monitoring/prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'forms-backend'
    static_configs:
      - targets: ['backend:8000']
    metrics_path: '/metrics'
    
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
```

#### Grafana Dashboards

Import pre-built dashboards:
1. Access Grafana at http://localhost:3001
2. Login with admin/admin
3. Import dashboards from `deployment/docker/monitoring/grafana/dashboards/`

### Backup Configuration

#### Automated Backups

```bash
# Set up automated backups
cd deployment/scripts
chmod +x setup-backup-cron.sh
./setup-backup-cron.sh install

# Verify backup schedule
./setup-backup-cron.sh status
```

#### Manual Backup

```bash
# Database backup
./backup.sh backup-db

# File storage backup
./backup.sh backup-files

# Complete backup
./backup.sh backup-all
```

## Security Hardening

### SSL/TLS Configuration

**Nginx SSL Configuration:**
```nginx
# Strong SSL configuration
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
ssl_prefer_server_ciphers off;
ssl_session_cache shared:SSL:10m;
ssl_session_timeout 10m;

# HSTS
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;

# Security headers
add_header X-Frame-Options DENY always;
add_header X-Content-Type-Options nosniff always;
add_header X-XSS-Protection "1; mode=block" always;
```

### Firewall Configuration

**UFW (Ubuntu):**
```bash
# Enable firewall
sudo ufw enable

# Allow SSH
sudo ufw allow 22/tcp

# Allow HTTP/HTTPS
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# Deny all other incoming
sudo ufw default deny incoming
sudo ufw default allow outgoing
```

### Container Security

**Docker Security:**
```yaml
# docker-compose.yml security settings
services:
  backend:
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp
    user: "1001:1001"
    cap_drop:
      - ALL
    cap_add:
      - NET_BIND_SERVICE
```

## Performance Optimization

### Database Performance

**Connection Pooling:**
```env
DB_MAX_CONNECTIONS=20
DB_MIN_CONNECTIONS=5
DB_ACQUIRE_TIMEOUT=30
DB_IDLE_TIMEOUT=600
```

**Query Optimization:**
- Enable query logging for slow queries
- Regular VACUUM and ANALYZE
- Monitor index usage
- Optimize frequently used queries

### Caching Strategy

**Redis Configuration:**
```env
REDIS_MAX_CONNECTIONS=10
REDIS_TIMEOUT=5
CACHE_TTL=3600
SESSION_TTL=86400
```

### Load Balancing

**Nginx Load Balancer:**
```nginx
upstream backend {
    server backend1:8000 weight=3;
    server backend2:8000 weight=2;
    server backend3:8000 weight=1;
    keepalive 32;
}

upstream frontend {
    server frontend1:3000;
    server frontend2:3000;
    keepalive 32;
}
```

## Troubleshooting

### Common Issues

#### Application Won't Start
```bash
# Check logs
docker-compose logs backend
docker-compose logs frontend

# Check configuration
./deploy.sh status

# Verify environment variables
docker-compose exec backend env | grep -E "(DATABASE|REDIS|JWT)"
```

#### Database Connection Issues
```bash
# Test database connection
docker-compose exec postgres psql -U forms -d forms -c "SELECT 1;"

# Check database logs
docker-compose logs postgres

# Verify credentials
echo $DATABASE_URL
```

#### Performance Issues
```bash
# Check resource usage
docker stats

# Monitor database performance
docker-compose exec postgres psql -U forms -d forms -c "
SELECT query, calls, total_time, mean_time 
FROM pg_stat_statements 
ORDER BY total_time DESC 
LIMIT 10;"

# Check Redis performance
docker-compose exec redis redis-cli info stats
```

### Log Analysis

**Application Logs:**
```bash
# Backend logs
docker-compose logs -f backend | jq '.'

# Frontend logs
docker-compose logs -f frontend

# Database logs
docker-compose logs postgres | grep ERROR

# Nginx access logs
docker-compose exec nginx tail -f /var/log/nginx/access.log
```

### Health Checks

```bash
# Application health
curl -f https://your-domain.com/health

# Database health
curl -f https://your-domain.com/api/health/database

# Detailed system status
curl -f https://your-domain.com/api/health/detailed
```

## Maintenance

### Regular Maintenance Tasks

**Daily:**
- Monitor system health
- Check backup status
- Review error logs
- Monitor disk space

**Weekly:**
- Update security patches
- Review performance metrics
- Clean up old logs
- Test backup restoration

**Monthly:**
- Update dependencies
- Review user access
- Performance optimization
- Security audit

### Update Procedures

```bash
# Update application
git pull origin main
./deploy.sh update

# Update dependencies
docker-compose pull
./deploy.sh restart

# Database migrations
docker-compose exec backend ./forms-backend migrate
```

---

**Deployment Guide Version**: 1.0  
**Last Updated**: December 2023  
**Support**: <EMAIL>
